#!/usr/bin/env python3
"""
Demonstration of the original bug that would occur before the fix.
This shows what the ValueError looked like.
"""

import os
import tempfile


def demonstrate_original_bug():
    """Demonstrate the original ValueError that would occur."""
    print("Demonstrating the original bug...")
    print("=" * 50)

    # Create a temporary directory with problematic files
    with tempfile.TemporaryDirectory() as temp_dir:
        cache_dir = os.path.join(temp_dir, "test_cache")
        os.makedirs(cache_dir, exist_ok=True)

        # Create files that would cause the original bug
        with open(os.path.join(cache_dir, "0.json"), "w") as f:
            f.write('{"content": "doc1"}')
        with open(os.path.join(cache_dir, "1.json"), "w") as f:
            f.write('{"content": "doc2"}')
        with open(os.path.join(cache_dir, "history"), "w") as f:
            f.write("history file")

        print(f"Files in cache: {os.listdir(cache_dir)}")

        # This is what the original code was doing (before the fix)
        print("\nOriginal problematic code:")
        print(
            "files = sorted(os.listdir(loading_dir), key=lambda x: int(x.split('.')[0]))"
        )

        try:
            # This would cause the ValueError
            files = sorted(os.listdir(cache_dir), key=lambda x: int(x.split(".")[0]))
            print(f"✗ This should have failed but didn't: {files}")
        except ValueError as e:
            print(f"✓ Expected ValueError occurred: {e}")
            print("This is exactly the bug that was reported!")

        print("\nFixed code (what we implemented):")
        print("Only processes files with numeric prefixes")

        def has_numeric_prefix(filename):
            try:
                int(filename.split(".")[0])
                return True
            except (ValueError, IndexError):
                return False

        # Filter for .json files with numeric prefixes and sort them
        json_files = [
            f
            for f in os.listdir(cache_dir)
            if f.endswith(".json") and has_numeric_prefix(f)
        ]
        files = sorted(json_files, key=lambda x: int(x.split(".")[0]))

        print(f"✓ Fixed code works: {files}")
        print("✓ Non-numeric files are safely ignored!")


if __name__ == "__main__":
    demonstrate_original_bug()
