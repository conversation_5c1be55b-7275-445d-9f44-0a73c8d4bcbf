#!/usr/bin/env python3
"""
Test script to verify the corrected caching logic for async_loading.py.

The caching should be per URL, not for all URLs combined.
"""

import asyncio
import tempfile
import os
import shutil
from src.rag.async_loading import AsyncLoadingEngine
from src.rag.settings import get_settings


async def test_per_url_caching():
    """Test that caching works per URL, not for all URLs combined."""
    print("Testing per-URL caching logic...")
    print("=" * 50)

    # Create a temporary directory for testing persistency
    temp_dir = tempfile.mkdtemp()
    print(f"Using temp directory: {temp_dir}")

    try:
        # Temporarily set loading_persistency
        original_persistency = get_settings().loading_persistency
        get_settings().loading_persistency = temp_dir

        engine = AsyncLoadingEngine()

        # Test URLs
        test_urls = ["https://httpbin.org/html", "https://httpbin.org/json"]

        print("\nTest 1: Load multiple URLs (should create separate cache dirs)")
        try:
            docs = await engine.load_web_docs(test_urls)
            print(f"✓ Loaded {len(docs)} documents from {len(test_urls)} URLs")

            # Check that separate cache directories were created
            cache_dirs = []
            for url in test_urls:
                dirs = url.replace("://", "/").split("/")
                cache_dir = os.path.join(temp_dir, *dirs)
                cache_dirs.append(cache_dir)
                if os.path.exists(cache_dir):
                    print(f"✓ Cache directory created for {url}: {cache_dir}")
                    files = os.listdir(cache_dir)
                    print(f"  - Contains {len(files)} files: {files}")
                else:
                    print(f"✗ No cache directory for {url}")

        except Exception as e:
            print(f"✗ Test 1 failed: {e}")
            return False

        print("\nTest 2: Load same URLs again (should use cache)")
        try:
            docs2 = await engine.load_web_docs(test_urls)
            print(f"✓ Loaded {len(docs2)} documents from cache")

            # Verify we got the same number of documents
            if len(docs) == len(docs2):
                print("✓ Same number of documents loaded from cache")
            else:
                print(f"✗ Different number of docs: {len(docs)} vs {len(docs2)}")

        except Exception as e:
            print(f"✗ Test 2 failed: {e}")
            return False

        print("\nTest 3: Load single URL (should work with existing cache)")
        try:
            single_docs = await engine.load_web_docs(test_urls[0])
            print(f"✓ Loaded {len(single_docs)} documents for single URL")

        except Exception as e:
            print(f"✗ Test 3 failed: {e}")
            return False

        print("\nTest 4: Verify cache structure")
        try:
            # Check the cache structure matches the sync version logic
            for url in test_urls:
                dirs = url.replace("://", "/").split("/")
                expected_path = os.path.join(temp_dir, *dirs)
                if os.path.exists(expected_path):
                    print(f"✓ Correct cache path for {url}: {expected_path}")
                    # Check for JSON files
                    json_files = [
                        f for f in os.listdir(expected_path) if f.endswith(".json")
                    ]
                    if json_files:
                        print(f"  - Contains {len(json_files)} JSON files")
                    else:
                        print(f"  - No JSON files found")
                else:
                    print(f"✗ Missing cache path for {url}: {expected_path}")

        except Exception as e:
            print(f"✗ Test 4 failed: {e}")
            return False

        print("\n" + "=" * 50)
        print("✅ All caching tests passed!")
        print("✅ Per-URL caching logic is working correctly!")
        return True

    finally:
        # Restore original setting and cleanup
        get_settings().loading_persistency = original_persistency
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
            print(f"Cleaned up temp directory: {temp_dir}")


async def test_no_persistency():
    """Test that loading works when persistency is disabled."""
    print("\nTesting without persistency...")
    print("=" * 30)

    try:
        # Temporarily disable persistency
        original_persistency = get_settings().loading_persistency
        get_settings().loading_persistency = None

        engine = AsyncLoadingEngine()

        test_urls = ["https://httpbin.org/html", "https://httpbin.org/json"]

        docs = await engine.load_web_docs(test_urls)
        print(f"✓ Loaded {len(docs)} documents without persistency")

        # Test single URL too
        single_docs = await engine.load_web_docs(test_urls[0])
        print(f"✓ Loaded {len(single_docs)} documents for single URL")

        return True

    except Exception as e:
        print(f"✗ No persistency test failed: {e}")
        return False

    finally:
        # Restore original setting
        get_settings().loading_persistency = original_persistency


if __name__ == "__main__":

    async def run_all_tests():
        print("🧪 Testing Corrected Caching Logic")
        print("=" * 60)

        test1_result = await test_per_url_caching()
        test2_result = await test_no_persistency()

        if test1_result and test2_result:
            print("\n🎉 All caching tests passed!")
            print("✅ Per-URL caching logic is correctly implemented")
            return True
        else:
            print("\n❌ Some caching tests failed")
            return False

    success = asyncio.run(run_all_tests())
    if success:
        print("\n✨ Caching fix verification: PASSED")
    else:
        print("\n🔧 Caching fix verification: FAILED")
        exit(1)
