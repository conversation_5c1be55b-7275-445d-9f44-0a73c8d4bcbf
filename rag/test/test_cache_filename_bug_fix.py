#!/usr/bin/env python3
"""
Test script to verify the bug fix for the ValueError in async_loading.py.

The bug was: ValueError: invalid literal for int() with base 10: 'history'
This occurred when the cache directory contained files with non-numeric prefixes
like 'history' that couldn't be converted to integers for sorting.
"""

import asyncio
import os
import tempfile
import json
from src.rag.async_loading import AsyncLoadingEngine


async def test_cache_filename_bug_fix():
    """Test that the bug fix works for cache directories with non-numeric filenames."""
    print("Testing the cache filename bug fix...")
    print("=" * 60)

    # Create a temporary cache directory with problematic files
    with tempfile.TemporaryDirectory() as temp_dir:
        cache_dir = os.path.join(temp_dir, "test_cache")
        os.makedirs(cache_dir, exist_ok=True)

        # Create valid JSON files with numeric prefixes (these should be loaded)
        valid_docs = [
            {"page_content": "Document 1", "metadata": {"source": "test1"}},
            {"page_content": "Document 2", "metadata": {"source": "test2"}},
        ]

        for i, doc_data in enumerate(valid_docs):
            with open(os.path.join(cache_dir, f"{i}.json"), "w") as f:
                json.dump(doc_data, f)

        # Create problematic files that should be ignored
        # 1. Non-JSON file with numeric prefix
        with open(os.path.join(cache_dir, "3.txt"), "w") as f:
            f.write("This is not a JSON file")

        # 2. File with non-numeric prefix (this was causing the ValueError)
        with open(os.path.join(cache_dir, "history"), "w") as f:
            f.write("Some history file")

        # 3. JSON file with non-numeric prefix
        with open(os.path.join(cache_dir, "metadata.json"), "w") as f:
            json.dump({"info": "metadata"}, f)

        # 4. File with complex non-numeric name
        with open(os.path.join(cache_dir, "backup_2023.json"), "w") as f:
            json.dump({"backup": "data"}, f)

        print(f"Created test cache directory: {cache_dir}")
        print(f"Files in cache: {os.listdir(cache_dir)}")

        # Test 1: Async version (this was failing before the fix)
        print("\nTest 1: Async version _load_from_cache")
        try:
            async_engine = AsyncLoadingEngine()
            docs = await async_engine._load_from_cache(cache_dir)
            print(f"✓ Async test passed: loaded {len(docs)} docs")

            # Verify we loaded the correct documents
            if len(docs) == 2:
                print("✓ Correct number of documents loaded")
                for i, doc in enumerate(docs):
                    expected_content = f"Document {i+1}"
                    if doc.page_content == expected_content:
                        print(f"✓ Document {i+1} content correct: {doc.page_content}")
                    else:
                        print(f"✗ Document {i+1} content incorrect: {doc.page_content}")
            else:
                print(f"✗ Expected 2 documents, got {len(docs)}")

        except ValueError as e:
            if "invalid literal for int()" in str(e):
                print(f"✗ ValueError bug still exists: {e}")
                return False
            else:
                print(f"✗ Different ValueError: {e}")
                return False
        except Exception as e:
            print(f"✗ Async test failed with different error: {e}")
            return False

        # Test 2: Verify the fix works (no ValueError)
        print("\nTest 2: Verify no ValueError occurs")
        try:
            # The key test is that we don't get a ValueError anymore
            # The async version should now handle non-numeric filenames gracefully
            print(f"✓ No ValueError occurred - bug is fixed!")
            print(f"✓ Async version loaded {len(docs)} documents successfully")

            # Verify we loaded only the expected documents (with numeric prefixes)
            if len(docs) == 2:
                print("✓ Correct number of documents loaded (only numeric prefixes)")
                for i, doc in enumerate(docs):
                    expected_content = f"Document {i+1}"
                    if doc.page_content == expected_content:
                        print(f"✓ Document {i+1} content correct: {doc.page_content}")
                    else:
                        print(f"✗ Document {i+1} content incorrect: {doc.page_content}")
            else:
                print(f"✗ Expected 2 documents, got {len(docs)}")
                print("This suggests the filtering logic may need adjustment")

        except Exception as e:
            print(f"✗ Unexpected error in verification: {e}")
            return False

        # Test 3: Edge cases
        print("\nTest 3: Edge cases")

        # Test with empty directory
        empty_dir = os.path.join(temp_dir, "empty_cache")
        os.makedirs(empty_dir, exist_ok=True)

        try:
            empty_docs = await async_engine._load_from_cache(empty_dir)
            if len(empty_docs) == 0:
                print("✓ Empty directory test passed")
            else:
                print(
                    f"✗ Empty directory test failed: expected 0 docs, got {len(empty_docs)}"
                )
        except Exception as e:
            print(f"✗ Empty directory test failed: {e}")
            return False

        # Test with directory containing only non-JSON files
        non_json_dir = os.path.join(temp_dir, "non_json_cache")
        os.makedirs(non_json_dir, exist_ok=True)

        with open(os.path.join(non_json_dir, "file1.txt"), "w") as f:
            f.write("text file")
        with open(os.path.join(non_json_dir, "history"), "w") as f:
            f.write("history file")

        try:
            non_json_docs = await async_engine._load_from_cache(non_json_dir)
            if len(non_json_docs) == 0:
                print("✓ Non-JSON directory test passed")
            else:
                print(
                    f"✗ Non-JSON directory test failed: expected 0 docs, got {len(non_json_docs)}"
                )
        except Exception as e:
            print(f"✗ Non-JSON directory test failed: {e}")
            return False

    print("\n" + "=" * 60)
    print("All tests completed successfully! Cache filename bug fix verified.")
    return True


if __name__ == "__main__":
    success = asyncio.run(test_cache_filename_bug_fix())
    if success:
        print("\n🎉 Cache filename bug fix verification: PASSED")
    else:
        print("\n❌ Cache filename bug fix verification: FAILED")
        exit(1)
