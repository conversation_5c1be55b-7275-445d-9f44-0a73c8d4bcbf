#!/usr/bin/env python3
"""
Verification script to ensure all async functions use correct templates.
"""

import sys
import os
import re

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "src"))


def check_file_for_hardcoded_prompts(file_path):
    """Check a file for hardcoded prompts using direct_generation template."""
    issues = []

    try:
        with open(file_path, "r") as f:
            content = f.read()
            lines = content.split("\n")

        # Look for patterns that indicate hardcoded prompts
        patterns = [
            (r'template_name="direct_generation"', "Uses direct_generation template"),
            (r'f""".*{.*}.*"""', "Potential f-string prompt"),
            (r'prompt\s*=\s*f"""', "F-string prompt assignment"),
        ]

        for i, line in enumerate(lines, 1):
            for pattern, description in patterns:
                if re.search(pattern, line):
                    # Check if this is a legitimate use of direct_generation
                    # (it should only be used for actual direct generation, not as a workaround)
                    if 'template_name="direct_generation"' in line:
                        # Look at surrounding context to see if this is legitimate
                        context_start = max(0, i - 5)
                        context_end = min(len(lines), i + 5)
                        context = "\n".join(lines[context_start:context_end])

                        # Check if the template_vars contain a hardcoded prompt
                        if 'template_vars={"question":' in context and (
                            'f"""' in context or "prompt" in context
                        ):
                            issues.append(
                                f"Line {i}: {description} - Likely hardcoded prompt"
                            )
                    else:
                        issues.append(f"Line {i}: {description}")

        return issues

    except Exception as e:
        return [f"Error reading file: {e}"]


def verify_template_usage():
    """Verify that async functions use proper templates."""
    print("🔍 Verifying Template Usage in Async Files")
    print("=" * 50)

    files_to_check = [
        "src/rag/async_pre_retrieval.py",
        "src/rag/async_judging.py",
        "src/rag/async_generation.py",
        "src/rag/async_post_retrieval.py",
    ]

    all_issues = []

    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"\n📁 Checking {file_path}...")
            issues = check_file_for_hardcoded_prompts(file_path)

            if issues:
                print(f"❌ Found {len(issues)} potential issues:")
                for issue in issues:
                    print(f"  - {issue}")
                all_issues.extend(issues)
            else:
                print("✅ No hardcoded prompts detected")
        else:
            print(f"⚠️  File not found: {file_path}")

    return len(all_issues) == 0


def verify_template_completeness():
    """Verify that all required templates exist."""
    print("\n🔍 Verifying Template Completeness")
    print("=" * 50)

    try:
        from rag.prompt_templates import ALL_DEFAULT_TEMPLATES

        # Expected templates for async operations
        expected_templates = {
            "multi_query": "Multi-query generation",
            "hyde": "HyDE generation",
            "query_decomposition": "Query decomposition",
            "query_step_back": "Step-back query generation",
            "retrieval_judging": "Retrieval necessity judging",
            "doc_relevance_judging": "Document relevance judging",
            "hallucination_judging": "Hallucination detection",
            "answer_judging": "Answer quality judging",
            "completeness_judging": "Answer completeness judging",
            "clarity_judging": "Answer clarity judging",
            "contextual_query_enhancement": "Contextual query enhancement",
        }

        missing_templates = []
        for template_name, description in expected_templates.items():
            if template_name not in ALL_DEFAULT_TEMPLATES:
                missing_templates.append(f"{template_name} ({description})")
            else:
                print(f"✅ {template_name}: {description}")

        if missing_templates:
            print(f"\n❌ Missing templates:")
            for template in missing_templates:
                print(f"  - {template}")
            return False

        print(f"\n✅ All {len(expected_templates)} required templates present")
        return True

    except Exception as e:
        print(f"❌ Error checking templates: {e}")
        return False


def verify_function_signatures():
    """Verify that async functions have correct signatures."""
    print("\n🔍 Verifying Function Signatures")
    print("=" * 50)

    try:
        # Check that functions can be imported
        from rag.async_pre_retrieval import (
            multi_query_generation_async,
            hyde_query_generation_async,
            step_back_query_generation_async,
            decomposition_query_generation_async,
        )

        print("✅ Pre-retrieval functions imported successfully")

        from rag.async_judging import (
            retrieval_judger_async,
            doc_relevance_judger_async,
            hallucination_judger_async,
            answer_judger_async,
        )

        print("✅ Judging functions imported successfully")

        return True

    except Exception as e:
        print(f"❌ Import error: {e}")
        return False


def main():
    """Run all verification checks."""
    print("🧪 Prompt Fix Verification")
    print("=" * 50)

    results = []

    # Check template completeness
    results.append(verify_template_completeness())

    # Check function signatures
    results.append(verify_function_signatures())

    # Check for hardcoded prompts
    results.append(verify_template_usage())

    # Summary
    print("\n" + "=" * 50)
    print("🧪 Verification Summary")
    print("=" * 50)

    passed = sum(results)
    total = len(results)

    if passed == total:
        print(f"✅ All {total} verification checks passed!")
        print("🎉 Prompt parity fixes successfully implemented!")
        print("\n📋 Summary of fixes:")
        print("  ✅ Fixed async_pre_retrieval.py to use proper templates")
        print("  ✅ Fixed async_judging.py to use proper templates")
        print("  ✅ Added missing judging templates")
        print("  ✅ Fixed step_back_query_generation_async")
        print("  ✅ Fixed contextual_query_enhancement")
        print("  ✅ All async functions now use template system")
    else:
        print(f"❌ {total - passed} out of {total} verification checks failed!")
        print("⚠️  Additional fixes may be needed!")

    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
