#!/usr/bin/env python3
"""
Simple test to verify prompt template fixes.
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "src"))


def test_templates():
    """Test that templates are properly loaded."""
    try:
        from rag.prompt_templates import ALL_DEFAULT_TEMPLATES

        print(f"✓ Loaded {len(ALL_DEFAULT_TEMPLATES)} templates")

        # Check key templates exist
        required_templates = [
            "multi_query",
            "hyde",
            "query_decomposition",
            "retrieval_judging",
            "doc_relevance_judging",
            "hallucination_judging",
            "answer_judging",
            "completeness_judging",
            "clarity_judging",
        ]

        missing = []
        for template in required_templates:
            if template not in ALL_DEFAULT_TEMPLATES:
                missing.append(template)

        if missing:
            print(f"❌ Missing templates: {missing}")
            return False

        print("✓ All required templates present")

        # Check that templates have proper placeholders
        multi_query = ALL_DEFAULT_TEMPLATES["multi_query"]
        if "{question}" not in multi_query:
            print("❌ multi_query template missing {question} placeholder")
            return False
        print("✓ multi_query template has correct placeholder")

        hyde = ALL_DEFAULT_TEMPLATES["hyde"]
        if "{question}" not in hyde:
            print("❌ hyde template missing {question} placeholder")
            return False
        print("✓ hyde template has correct placeholder")

        retrieval_judging = ALL_DEFAULT_TEMPLATES["retrieval_judging"]
        if "{question}" not in retrieval_judging:
            print("❌ retrieval_judging template missing {question} placeholder")
            return False
        print("✓ retrieval_judging template has correct placeholder")

        hallucination_judging = ALL_DEFAULT_TEMPLATES["hallucination_judging"]
        if (
            "{context}" not in hallucination_judging
            or "{answer}" not in hallucination_judging
        ):
            print("❌ hallucination_judging template missing placeholders")
            return False
        print("✓ hallucination_judging template has correct placeholders")

        print("✅ All template tests passed!")
        return True

    except Exception as e:
        print(f"❌ Template test failed: {e}")
        return False


def test_async_imports():
    """Test that async modules can be imported."""
    try:
        from rag.async_pre_retrieval import (
            multi_query_generation_async,
            hyde_query_generation_async,
            decomposition_query_generation_async,
        )

        print("✓ Async pre-retrieval functions imported")

        from rag.async_judging import (
            retrieval_judger_async,
            doc_relevance_judger_async,
            hallucination_judger_async,
            answer_judger_async,
        )

        print("✓ Async judging functions imported")

        from rag.async_generation import AsyncGenerationEngine

        print("✓ AsyncGenerationEngine imported")

        print("✅ All async imports successful!")
        return True

    except Exception as e:
        print(f"❌ Async import test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("🧪 Simple Prompt Test")
    print("=" * 30)

    results = []

    # Test templates
    results.append(test_templates())

    # Test async imports
    results.append(test_async_imports())

    # Summary
    print("\n" + "=" * 30)
    passed = sum(results)
    total = len(results)

    if passed == total:
        print(f"✅ All {total} tests passed!")
        return True
    else:
        print(f"❌ {total - passed} out of {total} tests failed!")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
