#!/usr/bin/env python3
"""
Complete test to verify both AsyncChunkSituatingEngine and AdvancedAsyncChunkSituatingEngine fixes.
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "src"))


def test_async_chunk_situating_engine_fix():
    """Test that AsyncChunkSituatingEngine.situate_chunk uses correct template."""
    print("🔍 Testing AsyncChunkSituatingEngine Fix")
    print("=" * 50)

    try:
        with open("src/rag/async_chunk_situating.py", "r") as f:
            content = f.read()

        # Check that AsyncChunkSituatingEngine.situate_chunk uses LLM
        if "from .async_generation import get_async_generation_engine" not in content:
            print("❌ AsyncChunkSituatingEngine doesn't import async generation engine")
            return False

        print("✅ AsyncChunkSituatingEngine imports async generation engine")

        # Check for correct template usage in main situate_chunk method
        lines = content.split("\n")
        in_main_situate_chunk = False
        uses_contextual_retrieval = False
        uses_correct_variables = False
        uses_correct_format = False

        for i, line in enumerate(lines):
            if "class AsyncChunkSituatingEngine:" in line:
                # Look for the situate_chunk method in this class
                for j in range(i, min(i + 100, len(lines))):
                    if "def situate_chunk(" in lines[j]:
                        in_main_situate_chunk = True
                    elif (
                        "def " in lines[j]
                        and in_main_situate_chunk
                        and "situate_chunk" not in lines[j]
                    ):
                        in_main_situate_chunk = False
                    elif in_main_situate_chunk:
                        if 'template_name="contextual_retrieval"' in lines[j]:
                            uses_contextual_retrieval = True
                        if '"doc_content": source_doc.page_content' in lines[j]:
                            uses_correct_variables = True
                        if 'f"{chunk.page_content}\\n\\n{contextual_info}"' in lines[j]:
                            uses_correct_format = True
                break

        if not uses_contextual_retrieval:
            print(
                "❌ AsyncChunkSituatingEngine.situate_chunk doesn't use contextual_retrieval template"
            )
            return False
        print(
            "✅ AsyncChunkSituatingEngine.situate_chunk uses contextual_retrieval template"
        )

        if not uses_correct_variables:
            print(
                "❌ AsyncChunkSituatingEngine.situate_chunk doesn't use correct template variables"
            )
            return False
        print(
            "✅ AsyncChunkSituatingEngine.situate_chunk uses correct template variables"
        )

        if not uses_correct_format:
            print(
                "❌ AsyncChunkSituatingEngine.situate_chunk doesn't use correct enhanced content format"
            )
            return False
        print(
            "✅ AsyncChunkSituatingEngine.situate_chunk uses correct enhanced content format"
        )

        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False


def test_advanced_async_chunk_situating_engine_fix():
    """Test that AdvancedAsyncChunkSituatingEngine.llm_based_situating uses correct template."""
    print("\n🔍 Testing AdvancedAsyncChunkSituatingEngine Fix")
    print("=" * 50)

    try:
        with open("src/rag/async_chunk_situating.py", "r") as f:
            content = f.read()

        # Check for correct template usage in llm_based_situating method
        lines = content.split("\n")
        in_llm_based_situating = False
        uses_contextual_retrieval = False
        uses_correct_variables = False
        uses_correct_format = False

        for i, line in enumerate(lines):
            if "def llm_based_situating(" in line:
                in_llm_based_situating = True
            elif "def " in line and in_llm_based_situating:
                in_llm_based_situating = False
            elif in_llm_based_situating:
                if 'template_name="contextual_retrieval"' in line:
                    uses_contextual_retrieval = True
                if '"doc_content": source_doc.page_content' in line:
                    uses_correct_variables = True
                if 'f"{chunk.page_content}\\n\\n{contextual_info}"' in line:
                    uses_correct_format = True

        if not uses_contextual_retrieval:
            print(
                "❌ AdvancedAsyncChunkSituatingEngine.llm_based_situating doesn't use contextual_retrieval template"
            )
            return False
        print(
            "✅ AdvancedAsyncChunkSituatingEngine.llm_based_situating uses contextual_retrieval template"
        )

        if not uses_correct_variables:
            print(
                "❌ AdvancedAsyncChunkSituatingEngine.llm_based_situating doesn't use correct template variables"
            )
            return False
        print(
            "✅ AdvancedAsyncChunkSituatingEngine.llm_based_situating uses correct template variables"
        )

        if not uses_correct_format:
            print(
                "❌ AdvancedAsyncChunkSituatingEngine.llm_based_situating doesn't use correct enhanced content format"
            )
            return False
        print(
            "✅ AdvancedAsyncChunkSituatingEngine.llm_based_situating uses correct enhanced content format"
        )

        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False


def test_no_hardcoded_prompts():
    """Test that no hardcoded prompts remain in chunk situating."""
    print("\n🔍 Testing No Hardcoded Prompts Remain")
    print("=" * 50)

    try:
        with open("src/rag/async_chunk_situating.py", "r") as f:
            content = f.read()

        # Check that there are no hardcoded prompts using direct_generation
        lines = content.split("\n")
        hardcoded_prompts = []

        for i, line in enumerate(lines, 1):
            # Look for patterns that indicate hardcoded prompts
            if 'template_name="direct_generation"' in line:
                # Check context to see if this is a legitimate use
                context_lines = lines[max(0, i - 5) : min(len(lines), i + 5)]
                context = "\n".join(context_lines)

                # If it's used with a hardcoded prompt, it's problematic
                if any(
                    pattern in context
                    for pattern in ['f"""', "prompt =", "situating_prompt"]
                ):
                    hardcoded_prompts.append(f"Line {i}: {line.strip()}")

        if hardcoded_prompts:
            print("❌ Found hardcoded prompts:")
            for prompt in hardcoded_prompts:
                print(f"  - {prompt}")
            return False

        print("✅ No hardcoded prompts found")

        # Check that old placeholder implementation is gone
        if "This is a simplified implementation" in content:
            print("⚠️  Old placeholder comment still present")

        if "For now, just add some basic contextual metadata" in content:
            print("❌ Old placeholder implementation still present")
            return False

        print("✅ Old placeholder implementation removed")

        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False


def test_sync_async_parity():
    """Test that sync and async versions have complete parity."""
    print("\n🔍 Testing Sync-Async Parity")
    print("=" * 50)

    try:
        # Check sync version
        with open("src/rag/chunk_situating.py", "r") as f:
            sync_content = f.read()

        # Check async version
        with open("src/rag/async_chunk_situating.py", "r") as f:
            async_content = f.read()

        # Both should use contextual_retrieval template
        sync_uses_template = (
            'ALL_DEFAULT_TEMPLATES["contextual_retrieval"]' in sync_content
        )
        async_uses_template = 'template_name="contextual_retrieval"' in async_content

        if not sync_uses_template:
            print("❌ Sync version doesn't use contextual_retrieval template")
            return False

        if not async_uses_template:
            print("❌ Async version doesn't use contextual_retrieval template")
            return False

        print("✅ Both sync and async use contextual_retrieval template")

        # Both should use same template variables
        sync_uses_vars = (
            '"doc_content": doc.page_content' in sync_content
            and '"chunk_content": chunk.page_content' in sync_content
        )
        async_uses_vars = (
            '"doc_content": source_doc.page_content' in async_content
            and '"chunk_content": chunk.page_content' in async_content
        )

        if not sync_uses_vars:
            print("❌ Sync version doesn't use correct template variables")
            return False

        if not async_uses_vars:
            print("❌ Async version doesn't use correct template variables")
            return False

        print("✅ Both sync and async use correct template variables")

        # Both should use same enhanced content format
        sync_format = (
            'f"{chunk.page_content}\\n\\n{llm_output.content}"' in sync_content
        )
        async_format = 'f"{chunk.page_content}\\n\\n{contextual_info}"' in async_content

        if not sync_format:
            print("⚠️  Sync version format might have changed")

        if not async_format:
            print("❌ Async version doesn't use correct enhanced content format")
            return False

        print("✅ Both sync and async use compatible enhanced content format")

        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("🧪 Complete Chunk Situating Fix Verification")
    print("=" * 60)

    results = []

    # Test AsyncChunkSituatingEngine fix
    results.append(test_async_chunk_situating_engine_fix())

    # Test AdvancedAsyncChunkSituatingEngine fix
    results.append(test_advanced_async_chunk_situating_engine_fix())

    # Test no hardcoded prompts remain
    results.append(test_no_hardcoded_prompts())

    # Test sync-async parity
    results.append(test_sync_async_parity())

    # Summary
    print("\n" + "=" * 60)
    print("🧪 Complete Chunk Situating Fix Summary")
    print("=" * 60)

    passed = sum(results)
    total = len(results)

    if passed == total:
        print(f"✅ All {total} tests passed!")
        print("🎉 Complete chunk situating template fix verified!")
        print("\n📋 Summary of fixes:")
        print(
            "  ✅ AsyncChunkSituatingEngine.situate_chunk now uses LLM with contextual_retrieval template"
        )
        print(
            "  ✅ AdvancedAsyncChunkSituatingEngine.llm_based_situating uses contextual_retrieval template"
        )
        print("  ✅ Both use correct template variables (doc_content, chunk_content)")
        print("  ✅ Both create enhanced content in same format as sync version")
        print("  ✅ No hardcoded prompts remain")
        print("  ✅ Complete prompt parity with sync version achieved")
        print(
            "\n🎯 Both AsyncChunkSituatingEngine and AdvancedAsyncChunkSituatingEngine are now fixed!"
        )
    else:
        print(f"❌ {total - passed} out of {total} tests failed!")
        print("⚠️  Additional fixes may be needed!")

    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
