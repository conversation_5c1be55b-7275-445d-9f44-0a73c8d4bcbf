import json
import logging
import os
import subprocess
import tempfile
from pathlib import Path

from fast_service import RequestContext
import openai
from pydantic import BaseModel

from webarena.agent import (
    PromptAgent,
    construct_agent,
    AgentArgs,
)
from webarena.agent.prompts import *
from webarena.browser_env import (
    Action,
    ActionTypes,
    StateInfo,
    action_to_model,
    create_stop_action,
)
from webarena.browser_env.trajectory import (
    Trajectory,
    trajectory_to_model,
)
from webarena.browser_env.actions import is_equivalent
from webarena.browser_env.helper_functions import (
    RenderHelper,
    get_action_description,
)
from webarena.fast_service_api import env_manager
from webarena.browser_env.env_args import (
    EnvInitArgs,
    EnvResetArgs,
    EnvStepArgs,
    EnvEvaluateArgs,
    EnvSaveTraceArgs,
    EnvDestroyArgs,
)
from webarena.browser_env.utils import observation_from_model

logger = logging.getLogger("logger")
logger.setLevel(logging.INFO)

console_handler = logging.StreamHandler()
console_handler.setLevel(logging.DEBUG)
logger.addHandler(console_handler)


def get_site_comb_from_filepath(file_path: str) -> list[str]:
    comb = os.path.basename(file_path).rsplit("_", 1)[0].split(".")
    return comb


def early_stop(
    trajectory: Trajectory, max_steps: int, thresholds: dict[str, int]
) -> tuple[bool, str]:
    """Check whether need to early stop"""

    # reach the max step
    num_steps = (len(trajectory) - 1) / 2
    if num_steps >= max_steps:
        return True, f"Reach max steps {max_steps}"

    last_k_actions: list[Action]
    action_seq: list[Action]

    # Case: parsing failure for k times
    k = thresholds["parsing_failure"]
    last_k_actions = trajectory[1::2][-k:]  # type: ignore[assignment]
    if len(last_k_actions) >= k:
        if all(
            [action["action_type"] == ActionTypes.NONE for action in last_k_actions]
        ):
            return True, f"Failed to parse actions for {k} times"

    # Case: same action for k times
    k = thresholds["repeating_action"]
    last_k_actions = trajectory[1::2][-k:]  # type: ignore[assignment]
    action_seq = trajectory[1::2]  # type: ignore[assignment]

    if len(action_seq) == 0:
        return False, ""

    last_action: Action = action_seq[-1]

    if last_action["action_type"] != ActionTypes.TYPE:
        if len(last_k_actions) >= k:
            if all([is_equivalent(action, last_action) for action in last_k_actions]):
                return True, f"Same action for {k} times"

    else:
        # check the action sequence
        if sum([is_equivalent(action, last_action) for action in action_seq]) >= k:
            return True, f"Same typing action for {k} times"

    return False, ""


class E2eExecutionArgs(AgentArgs):
    max_steps: int
    parsing_failure_th: int
    repeating_action_failure_th: int
    render: bool
    slow_mo: int
    observation_type: str
    current_viewport_only: bool
    viewport_width: int
    viewport_height: int
    save_trace_enabled: bool
    sleep_after_execution: float
    action_set_tag: str
    result_dir: str
    render_screenshot: bool
    config_file: str


class E2eExecutionRetVal(BaseModel):
    score: float


def e2e_execution(
    args: E2eExecutionArgs,
    with_evaluation: bool = True,
    fsm_context: RequestContext = None,
) -> E2eExecutionRetVal:
    config_file: str = args.config_file

    agent = construct_agent(args)
    max_steps = args.max_steps

    early_stop_thresholds = {
        "parsing_failure": args.parsing_failure_th,
        "repeating_action": args.repeating_action_failure_th,
    }

    env_id = env_manager.env_init(
        EnvInitArgs(
            headless=not args.render,
            slow_mo=args.slow_mo,
            observation_type=args.observation_type,
            current_viewport_only=args.current_viewport_only,
            viewport_size={
                "width": args.viewport_width,
                "height": args.viewport_height,
            },
            save_trace_enabled=args.save_trace_enabled,
            sleep_after_execution=args.sleep_after_execution,
        ),
        fsm_context=fsm_context,
    ).env_id

    try:
        render_helper = RenderHelper(config_file, args.result_dir, args.action_set_tag)

        # get intent
        with open(config_file) as f:
            _c = json.load(f)
            intent = _c["intent"]
            task_id = _c["task_id"]
            # automatically login
            if _c["storage_state"]:
                cookie_file_name = os.path.basename(_c["storage_state"])
                comb = get_site_comb_from_filepath(cookie_file_name)
                temp_dir = tempfile.mkdtemp()
                # subprocess to renew the cookie
                subprocess.run(
                    [
                        "python",
                        "scripts/auto_login.py",
                        "--auth_folder",
                        temp_dir,
                        "--site_list",
                        *comb,
                    ]
                )
                _c["storage_state"] = f"{temp_dir}/{cookie_file_name}"
                assert os.path.exists(_c["storage_state"])
                # update the config file
                config_file = f"{temp_dir}/{os.path.basename(config_file)}"
                with open(config_file, "w") as f:
                    json.dump(_c, f)

        logger.info(f"[Config file]: {config_file}")
        logger.info(f"[Intent]: {intent}")

        agent.reset(config_file)
        trajectory: Trajectory = []
        ret = env_manager.env_reset(
            EnvResetArgs(env_id=env_id, options={"config_file": config_file}),
            fsm_context=fsm_context,
        )
        obs = {k: observation_from_model(v) for k, v in ret.observation.items()}
        state_info = StateInfo(
            observation=obs,
            info=ret.info,
        )
        trajectory.append(state_info)

        meta_data = {"action_history": ["None"]}
        while True:
            early_stop_flag, stop_info = early_stop(
                trajectory, max_steps, early_stop_thresholds
            )

            if early_stop_flag:
                action = create_stop_action(f"Early stop: {stop_info}")
            else:
                try:
                    action = agent.next_action(
                        trajectory,
                        intent,
                        meta_data=meta_data,
                        fsm_context=fsm_context,
                    )
                except ValueError as e:
                    # get the error message
                    action = create_stop_action(f"ERROR: {str(e)}")

            trajectory.append(action)

            action_str = get_action_description(
                action,
                state_info.info.observation_metadata,
                action_set_tag=args.action_set_tag,
                prompt_constructor=(
                    agent.prompt_constructor if isinstance(agent, PromptAgent) else None
                ),
            )
            render_helper.render(action, state_info, meta_data, args.render_screenshot)
            meta_data["action_history"].append(action_str)

            if action["action_type"] == ActionTypes.STOP:
                break

            ret = env_manager.env_step(
                EnvStepArgs(env_id=env_id, action=action_to_model(action)),
                fsm_context=fsm_context,
            )
            obs = ret.observation
            terminated = ret.done
            info = ret.info
            obs = {k: observation_from_model(v) for k, v in obs.items()}
            state_info = StateInfo(
                observation=obs,
                info=info,
            )
            trajectory.append(state_info)

            if terminated:
                # add a action place holder
                trajectory.append(create_stop_action(""))
                break

        if with_evaluation:
            score = env_manager.env_evaluate(
                EnvEvaluateArgs(
                    env_id=env_id,
                    trajectory=trajectory_to_model(trajectory),
                    config_file=config_file,
                ),
                fsm_context=fsm_context,
            ).score

            if score == 1:
                logger.info(f"[Result] (PASS) {config_file}")
            else:
                logger.info(f"[Result] (FAIL) {config_file}")
        else:
            score = 1

        if args.save_trace_enabled:
            env_manager.env_save_trace(
                EnvSaveTraceArgs(
                    env_id=env_id,
                    trace_path=str(Path(args.result_dir) / "traces" / f"{task_id}.zip"),
                ),
                fsm_context=fsm_context,
            )

    except openai.OpenAIError as e:
        logger.info(f"[OpenAI Error] {repr(e)}")
        score = 0
    except Exception as e:
        logger.info(f"[Unhandled Error] {repr(e)}]")
        import traceback

        # write to error file
        with open(Path(args.result_dir) / "error.txt", "a") as f:
            f.write(f"[Config file]: {config_file}\n")
            f.write(f"[Unhandled Error] {repr(e)}\n")
            f.write(traceback.format_exc())  # write stack trace to file
        score = 0
    finally:
        render_helper.close()
        env_manager.env_destroy(EnvDestroyArgs(env_id=env_id), fsm_context=fsm_context)
        return E2eExecutionRetVal(score=score)
