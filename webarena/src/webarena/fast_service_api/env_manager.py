import os

import webarena.browser_env.env_manager as multi_thread_env_manager
import webarena.browser_env.multi_proc as multi_process_env_manager

from webarena.browser_env.env_args import (
    EnvInitArgs,
    EnvInitRetVal,
    EnvResetArgs,
    EnvResetRetVal,
    EnvStepArgs,
    EnvStepRetVal,
    EnvEvaluateArgs,
    EnvEvaluateRetVal,
    EnvSaveTraceArgs,
    EnvSaveTraceRetVal,
    EnvDestroyArgs,
    EnvDestroyRetVal,
)

from fast_service import RequestContext
from ..fast_service_manager import WEBARENA_FSM

USING_MULTIPROCESS = (
    os.environ.get("USING_MULTIPROCESS", "False").strip().lower() == "true"
)

ENV_MANAGER = (
    multi_process_env_manager if USING_MULTIPROCESS else multi_thread_env_manager
)


@WEBARENA_FSM.fast_service
def env_init(
    args: EnvInitArgs,
    fsm_context: RequestContext = None,
) -> EnvInitRetVal:
    return ENV_MANAGER.env_init(args, fsm_context)


@WEBARENA_FSM.fast_service
def env_step(
    args: EnvStepArgs,
    fsm_context: RequestContext = None,
) -> EnvStepRetVal:
    return ENV_MANAGER.env_step(args, fsm_context)


@WEBARENA_FSM.fast_service
def env_reset(
    args: EnvResetArgs,
    fsm_context: RequestContext = None,
) -> EnvResetRetVal:
    return ENV_MANAGER.env_reset(args, fsm_context)


@WEBARENA_FSM.fast_service
def env_evaluate(
    args: EnvEvaluateArgs,
    fsm_context: RequestContext = None,
) -> EnvEvaluateRetVal:
    return ENV_MANAGER.env_evaluate(args, fsm_context)


@WEBARENA_FSM.fast_service
def env_save_trace(
    args: EnvSaveTraceArgs,
    fsm_context: RequestContext = None,
) -> EnvSaveTraceRetVal:
    return ENV_MANAGER.env_save_trace(args, fsm_context)


@WEBARENA_FSM.fast_service
def env_destroy(
    args: EnvDestroyArgs,
    fsm_context: RequestContext = None,
) -> EnvDestroyRetVal:
    return ENV_MANAGER.env_destroy(args, fsm_context)
