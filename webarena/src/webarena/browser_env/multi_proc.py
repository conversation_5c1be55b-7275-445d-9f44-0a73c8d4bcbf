import os
import uuid
import threading

from multiprocessing import Process, Queue, Event
from queue import Empty
from threading import Thread, Lock, Semaphore
from concurrent.futures import Future

from fast_service import RequestContext

from webarena.browser_env import action_from_model
from webarena.browser_env.utils import observation_to_model
from webarena.browser_env import ScriptBrowserEnv
from webarena.evaluation_harness import evaluator_router
from webarena.browser_env.trajectory import trajectory_from_model
from webarena.browser_env.env_args import (
    EnvInitArgs,
    EnvInitRetVal,
    EnvResetArgs,
    EnvResetRetVal,
    EnvStepArgs,
    EnvStepRetVal,
    EnvEvaluateArgs,
    EnvEvaluateRetVal,
    EnvSaveTraceArgs,
    EnvSaveTraceRetVal,
    EnvDestroyArgs,
    EnvDestroyRetVal,
)
from webarena.fast_service_manager import WEBARENA_FSM


@WEBARENA_FSM.fast_service(monitor_only=True)
def execute_env_init(
    args: EnvInitArgs, fsm_context: RequestContext
) -> ScriptBrowserEnv:
    return ScriptBrowserEnv(
        headless=args.headless,
        slow_mo=args.slow_mo,
        observation_type=args.observation_type,
        current_viewport_only=args.current_viewport_only,
        viewport_size=args.viewport_size,
        save_trace_enabled=args.save_trace_enabled,
        sleep_after_execution=args.sleep_after_execution,
    )


@WEBARENA_FSM.fast_service(monitor_only=True)
def execute_env_destroy(env: ScriptBrowserEnv, fsm_context: RequestContext):
    env.close()


@WEBARENA_FSM.fast_service(monitor_only=True)
def execute_env_step(
    env: ScriptBrowserEnv, args: EnvStepArgs, fsm_context: RequestContext
) -> EnvStepRetVal:
    ret = env.step(action_from_model(args.action), fsm_context=fsm_context)
    obs = {k: observation_to_model(v) for k, v in ret[0].items()}
    return EnvStepRetVal(
        observation=obs,
        reward=ret[1],
        done=ret[2],
        timeout=ret[3],
        info=ret[4],
    )


@WEBARENA_FSM.fast_service(monitor_only=True)
def execute_env_reset(
    env: ScriptBrowserEnv, args: EnvResetArgs, fsm_context: RequestContext
) -> EnvResetRetVal:
    ret = env.reset(
        options=args.options,
    )
    obs = {k: observation_to_model(v) for k, v in ret[0].items()}
    return EnvResetRetVal(
        observation=obs,
        info=ret[1],
    )


@WEBARENA_FSM.fast_service(monitor_only=True)
def execute_env_evaluate(
    env: ScriptBrowserEnv, args: EnvEvaluateArgs, fsm_context: RequestContext
) -> EnvEvaluateRetVal:
    evaluator = evaluator_router(
        args.config_file,
    )
    score = evaluator(
        trajectory=trajectory_from_model(trajectory_from_model(args.trajectory)),
        config_file=args.config_file,
        page=env.page,
        client=env.get_page_client(env.page),
    )
    return EnvEvaluateRetVal(score=score)


@WEBARENA_FSM.fast_service(monitor_only=True)
def execute_env_save_trace(
    env: ScriptBrowserEnv, args: EnvSaveTraceArgs, fsm_context: RequestContext
):
    env.save_trace(
        args.trace_path,
    )


def env_runtime(
    env_id: str,
    msg_queue: Queue,
    result_queue: Queue,
    stop_event: Event,
):
    from fast_service import FastServiceConfig

    config = FastServiceConfig.load_from_file(
        "/workspace/webarena/config/env_manager.yml"
    )
    config.client_mode = True
    config.monitor_storage.store_dir = os.path.join(
        config.monitor_storage.store_dir, "envs", env_id
    )
    config.service_dict = {}
    WEBARENA_FSM.setup_client_mode(config)

    env = None
    while not stop_event.is_set():
        try:
            # The timeout makes sure the process could exit properly, which ensures health execution of env destroy.
            req_id, request_id, call_id, func, arg_json = msg_queue.get(timeout=1)
        except Empty:
            continue

        fsm_context = RequestContext(request_id=request_id, call_id=call_id)

        try:
            if func == "create":
                env_args: EnvInitArgs = EnvInitArgs.model_validate_json(arg_json)
                env = execute_env_init(env_args, fsm_context=fsm_context)
                result = (True, True)
            else:
                if env is None:
                    result = (False, "Environment not initialized")
                elif func == "destroy":
                    execute_env_destroy(env, fsm_context=fsm_context)
                    result = (True, True)
                elif func == "step":
                    env_args: EnvStepArgs = EnvStepArgs.model_validate_json(arg_json)
                    step_result: EnvStepRetVal = execute_env_step(
                        env, env_args, fsm_context=fsm_context
                    )
                    result = (True, step_result.model_dump_json())
                elif func == "reset":
                    env_args: EnvResetArgs = EnvResetArgs.model_validate_json(arg_json)
                    reset_result = execute_env_reset(
                        env, env_args, fsm_context=fsm_context
                    )
                    result = (True, reset_result.model_dump_json())
                elif func == "evaluate":
                    env_args: EnvEvaluateArgs = EnvEvaluateArgs.model_validate_json(
                        arg_json
                    )
                    evaluate_result = execute_env_evaluate(
                        env, env_args, fsm_context=fsm_context
                    )
                    result = (True, evaluate_result.model_dump_json())
                elif func == "save_trace":
                    env_args: EnvSaveTraceArgs = EnvSaveTraceArgs.model_validate_json(
                        arg_json
                    )
                    execute_env_save_trace(env, env_args, fsm_context=fsm_context)
                    result = (True, True)
                else:
                    result = (False, "Unrecognized function")
        except Exception as e:
            result = (False, e)

        result_queue.put((req_id, result))

    # exit elegantly
    WEBARENA_FSM.stop_client()


class EnvWorker:

    env_id: str
    worker_proc: Process
    msg_queue: Queue
    result_queue: Queue
    stop_event: Event

    def __init__(self, env_id: str, result_queue: Queue):

        self.result_queue = result_queue

        self.msg_queue = Queue()
        self.stop_event = Event()
        self.worker_proc = Process(
            target=env_runtime,
            args=(env_id, self.msg_queue, self.result_queue, self.stop_event),
        )

    def start(self):
        self.worker_proc.start()

    def stop(self):
        self.stop_event.set()
        self.worker_proc.join()

    def submit_task(
        self, req_id: str, fsm_context: RequestContext, func: str, arg_json: str
    ):
        self.msg_queue.put(
            (req_id, fsm_context.request_id, fsm_context.call_id, func, arg_json)
        )


class EnvWorkerManager:

    env_lock: Lock
    env_dict: dict[str, EnvWorker]

    req_lock: Lock
    req_dict: dict[str, Future]

    result_collector: Thread
    result_queue: Queue
    on_fly_semaphore: Semaphore

    def __init__(self):
        self.env_lock = Lock()
        self.env_dict: dict[str, EnvWorker] = {}

        self.req_lock = Lock()
        self.req_dict: dict[str, Future] = {}

        self.result_queue = Queue()
        self.on_fly_semaphore = Semaphore(0)
        self.result_collector = Thread(target=self.collect_results)
        self.result_collector.start()

    def collect_results(self):
        main_thread = threading.main_thread()
        while main_thread.is_alive():
            if not self.on_fly_semaphore.acquire(timeout=1):
                # Since this will be a module level singleton, the status of this thread affects whether the main process
                # could exit properly. Coordinator needs to log in before detailed env step, and the login operation relies
                # on a triggered scripts which imports this module. In that case, the script won't exit if this thread do not
                # exit, as a result of which the coordinator cannot finish login operation properly.
                continue

            req_id, env_result = self.result_queue.get()

            with self.req_lock:
                future = self.req_dict[req_id]
                self.req_dict.pop(req_id)

            if env_result[0]:
                future.set_result(env_result[1])
            else:
                future.set_exception(Exception(env_result[1]))

    def submit_task(
        self, env_id: str, fsm_context: RequestContext, func: str, arg_json: str = None
    ) -> Future:
        future = Future()

        if func == "create":
            env_worker = EnvWorker(env_id, result_queue=self.result_queue)

            with self.env_lock:
                if env_id in self.env_dict:
                    future.set_exception(
                        ValueError(f"Environment with id {env_id} already exists")
                    )
                    return future
                self.env_dict[env_id] = env_worker

            env_worker.start()

        with self.env_lock:
            env_worker = self.env_dict[env_id]
        req_id = str(uuid.uuid4())

        env_worker.submit_task(req_id, fsm_context, func, arg_json)
        with self.req_lock:
            self.req_dict[req_id] = future
        self.on_fly_semaphore.release()

        if func == "destroy":
            env_worker.stop()
            with self.env_lock:
                if env_id not in self.env_dict:
                    future.set_exception(
                        ValueError(f"Environment with id {env_id} does not exist")
                    )
                    return future
                self.env_dict.pop(env_id)

        return future


_env_counter = 0
_env_counter_lock = Lock()
_env_worker_manager = EnvWorkerManager()


def env_init(
    args: EnvInitArgs,
    fsm_context: RequestContext = None,
) -> EnvInitRetVal:
    global _env_counter

    with _env_counter_lock:
        env_id = _env_counter
        _env_counter += 1

    env_id = str(env_id)

    try:
        _env_worker_manager.submit_task(
            env_id,
            fsm_context,
            "create",
            args.model_dump_json(),
        ).result()
    except Exception as e:
        raise Exception(f"Failed to create environment: {e}")

    return EnvInitRetVal(env_id=env_id)


def env_step(
    args: EnvStepArgs,
    fsm_context: RequestContext = None,
) -> EnvStepRetVal:
    try:
        ret = _env_worker_manager.submit_task(
            args.env_id,
            fsm_context,
            "step",
            args.model_dump_json(),
        ).result()
        return EnvStepRetVal.model_validate_json(ret)
    except Exception as e:
        raise Exception(f"Failed to env_step: {e}")


def env_reset(
    args: EnvResetArgs,
    fsm_context: RequestContext = None,
) -> EnvResetRetVal:
    try:
        ret = _env_worker_manager.submit_task(
            args.env_id,
            fsm_context,
            "reset",
            args.model_dump_json(),
        ).result()
        return EnvResetRetVal.model_validate_json(ret)
    except Exception as e:
        raise Exception(f"Failed to env_reset: {e}")


def env_evaluate(
    args: EnvEvaluateArgs,
    fsm_context: RequestContext = None,
) -> EnvEvaluateRetVal:
    try:
        score = _env_worker_manager.submit_task(
            args.env_id,
            fsm_context,
            "evaluate",
            args.model_dump_json(),
        ).result()
        return EnvEvaluateRetVal.model_validate_json(score)
    except Exception as e:
        raise Exception(f"Failed to env_evaluate: {e}")


def env_save_trace(
    args: EnvSaveTraceArgs,
    fsm_context: RequestContext = None,
) -> EnvSaveTraceRetVal:
    try:
        success = _env_worker_manager.submit_task(
            args.env_id,
            fsm_context,
            "save_trace",
            args.model_dump_json(),
        ).result()
        return EnvSaveTraceRetVal(success=success)
    except Exception as e:
        raise Exception(f"Failed to env_save_trace: {e}")


def env_destroy(
    args: EnvDestroyArgs,
    fsm_context: RequestContext = None,
) -> EnvDestroyRetVal:
    try:
        success = _env_worker_manager.submit_task(
            args.env_id,
            fsm_context,
            "destroy",
        ).result()
        return EnvDestroyRetVal(success=success)
    except Exception as e:
        raise Exception(f"Failed to env_destroy: {e}")
