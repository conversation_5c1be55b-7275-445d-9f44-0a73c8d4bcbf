from pydantic import BaseModel

from webarena.browser_env import ScriptBrowserEnv

from fast_service import RequestContext
from webarena.browser_env import ActionModel, action_from_model, EnvInfo
from webarena.browser_env.utils import ObservationModel, observation_to_model
from webarena.browser_env.trajectory import TrajectoryModel, trajectory_from_model
from webarena.browser_env.env_args import (
    EnvInitArgs,
    EnvInitRetVal,
    EnvResetArgs,
    EnvResetRetVal,
    EnvStepArgs,
    EnvStepRetVal,
    EnvEvaluateArgs,
    EnvEvaluateRetVal,
    EnvSaveTraceArgs,
    EnvSaveTraceRetVal,
    EnvDestroyArgs,
    EnvDestroyRetVal,
)


from webarena.evaluation_harness import evaluator_router

from concurrent.futures import ThreadPoolExecutor, Future
from queue import Queue
from threading import Lock, Thread
from typing import Callable, Dict, Optional


class EnvThreadManager:
    def __init__(self, max_threads: int | None = None):
        self.executor = ThreadPoolExecutor(max_threads)
        self.env_thread_map: Dict[str, Queue] = {}
        self.lock = Lock()

    def submit_task(self, env_id: str, func: Callable | str, *args, **kwargs) -> Future:
        with self.lock:
            if env_id not in self.env_thread_map:
                raise ValueError(f"Environment with id {env_id} does not exist")

        # Task submission doesn't require holding the lock
        thread_queue = self.env_thread_map[env_id]
        future = Future()
        thread_queue.put((func, args, kwargs, future))
        return future

    def create_env(self, env_id: str, *args, **kwargs) -> Future:
        future = Future()
        with self.lock:
            if env_id in self.env_thread_map:
                future.set_exception(
                    ValueError(f"Environment with id {env_id} already exists")
                )
                return future

            # Create a thread-specific task queue
            thread_queue = Queue()
            self.env_thread_map[env_id] = thread_queue
            Thread(
                target=self._thread_worker,
                args=(thread_queue,),
                daemon=True,
            ).start()

        thread_queue.put(("create", args, kwargs, future))
        return future

    def destroy_env(self, env_id: str) -> Future:
        future = Future()
        with self.lock:
            if env_id not in self.env_thread_map:
                future.set_exception(
                    ValueError(f"Environment with id {env_id} does not exist")
                )
                return future

            thread_queue = self.env_thread_map.pop(env_id)

        thread_queue.put(("destroy", (), {}, future))
        return future

    def _thread_worker(self, task_queue: Queue):
        while True:
            func, args, kwargs, future = task_queue.get()
            try:
                if func == "create":
                    env = ScriptBrowserEnv(*args, **kwargs)
                    future.set_result(True)
                elif func == "destroy":
                    env.close()
                    future.set_result(True)
                    break
                elif func == "step":
                    result = env.step(*args, **kwargs)
                    future.set_result(result)
                elif func == "reset":
                    result = env.reset(*args, **kwargs)
                    future.set_result(result)
                elif func == "evaluate":
                    evaluator = evaluator_router(kwargs["config_file"])
                    score = evaluator(
                        trajectory=trajectory_from_model(kwargs["trajectory"]),
                        config_file=kwargs["config_file"],
                        page=env.page,
                        client=env.get_page_client(env.page),
                    )
                    future.set_result(score)
                elif func == "save_trace":
                    env.save_trace(*args, **kwargs)
                    future.set_result(True)
                else:
                    result = func(*args, **kwargs)
                    future.set_result(result)
            except Exception as e:
                future.set_exception(e)


_env_counter = 0
_env_counter_lock = Lock()
_env_thread_manager = EnvThreadManager()


def env_init(
    args: EnvInitArgs,
    fsm_context: RequestContext = None,
) -> EnvInitRetVal:
    global _env_counter

    with _env_counter_lock:
        env_id = _env_counter
        _env_counter += 1

    env_id = str(env_id)

    try:
        _env_thread_manager.create_env(
            env_id=env_id,
            headless=args.headless,
            slow_mo=args.slow_mo,
            observation_type=args.observation_type,
            current_viewport_only=args.current_viewport_only,
            viewport_size=args.viewport_size,
            save_trace_enabled=args.save_trace_enabled,
            sleep_after_execution=args.sleep_after_execution,
        ).result()
    except Exception as e:
        raise Exception(f"Failed to create environment: {e}")

    return EnvInitRetVal(env_id=env_id)


def env_step(
    args: EnvStepArgs,
    fsm_context: RequestContext = None,
) -> EnvStepRetVal:
    try:
        ret = _env_thread_manager.submit_task(
            args.env_id,
            "step",
            action_from_model(args.action),
            fsm_context=fsm_context,
        ).result()
        obs = {k: observation_to_model(v) for k, v in ret[0].items()}
        return EnvStepRetVal(
            observation=obs,
            reward=ret[1],
            done=ret[2],
            timeout=ret[3],
            info=ret[4],
        )
    except Exception as e:
        raise Exception(f"Failed to env_step: {e}")


def env_reset(
    args: EnvResetArgs,
    fsm_context: RequestContext = None,
) -> EnvResetRetVal:
    try:
        ret = _env_thread_manager.submit_task(
            args.env_id,
            "reset",
            options=args.options,
        ).result()
        obs = {k: observation_to_model(v) for k, v in ret[0].items()}
        return EnvResetRetVal(
            observation=obs,
            info=ret[1],
        )
    except Exception as e:
        raise Exception(f"Failed to env_reset: {e}")


def env_evaluate(
    args: EnvEvaluateArgs,
    fsm_context: RequestContext = None,
) -> EnvEvaluateRetVal:
    try:
        score = _env_thread_manager.submit_task(
            args.env_id,
            "evaluate",
            trajectory=trajectory_from_model(args.trajectory),
            config_file=args.config_file,
        ).result()
        return EnvEvaluateRetVal(score=score)
    except Exception as e:
        raise Exception(f"Failed to env_evaluate: {e}")


def env_save_trace(
    args: EnvSaveTraceArgs,
    fsm_context: RequestContext = None,
) -> EnvSaveTraceRetVal:
    try:
        success = _env_thread_manager.submit_task(
            args.env_id,
            "save_trace",
            args.trace_path,
        ).result()
        return EnvSaveTraceRetVal(success=success)
    except Exception as e:
        raise Exception(f"Failed to env_save_trace: {e}")


def env_destroy(
    args: EnvDestroyArgs,
    fsm_context: RequestContext = None,
) -> EnvDestroyRetVal:
    try:
        success = _env_thread_manager.destroy_env(args.env_id).result()
        return EnvDestroyRetVal(success=success)
    except Exception as e:
        raise Exception(f"Failed to env_destroy: {e}")
