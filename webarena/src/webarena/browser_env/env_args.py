from typing import Optional
from pydantic import BaseModel
from playwright.sync_api import ViewportSize

from webarena.browser_env import ActionModel, EnvInfo
from webarena.browser_env.utils import ObservationModel
from webarena.browser_env.trajectory import TrajectoryModel


class EnvDestroyArgs(BaseModel):
    env_id: str


class EnvDestroyRetVal(BaseModel):
    success: bool


class EnvInitArgs(BaseModel):
    headless: bool
    slow_mo: int
    observation_type: str
    current_viewport_only: bool
    viewport_size: ViewportSize
    save_trace_enabled: bool
    sleep_after_execution: float


class EnvInitRetVal(BaseModel):
    env_id: str


class EnvStepArgs(BaseModel):
    env_id: str
    action: ActionModel


class EnvStepRetVal(BaseModel):
    observation: dict[str, ObservationModel]
    reward: float
    done: bool
    timeout: bool
    info: EnvInfo


class EnvResetArgs(BaseModel):
    env_id: str
    options: Optional[dict[str, str]]


class EnvResetRetVal(BaseModel):
    observation: dict[str, ObservationModel]
    info: EnvInfo


class EnvEvaluateArgs(BaseModel):
    env_id: str
    trajectory: TrajectoryModel
    config_file: str


class EnvEvaluateRetVal(BaseModel):
    score: float


class EnvSaveTraceArgs(BaseModel):
    env_id: str
    trace_path: str


class EnvSaveTraceRetVal(BaseModel):
    success: bool
