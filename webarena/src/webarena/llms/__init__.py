"""This module is adapt from https://github.com/zeno-ml/zeno-build"""

from .providers.hf_utils import generate_from_huggingface_completion
from .providers.openai_utils import (
    generate_from_openai_chat_completion,
    generate_from_openai_completion,
    generate_from_openai_completion_with_trace,
)
from .utils import call_llm, CallLlmArgs, CallLlmRetVal
from .lm_config import LlmConfigArgs

__all__ = [
    "generate_from_openai_completion",
    "generate_from_openai_chat_completion",
    "generate_from_huggingface_completion",
    "generate_from_openai_completion_with_trace",
    "call_llm",
    "CallLlmArgs",
    "CallLlmRetVal",
    "LlmConfigArgs",
]
