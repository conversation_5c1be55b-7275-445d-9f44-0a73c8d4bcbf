client_mode: True
service_impl: fastapi
service_list:
  - module_name: webarena.fast_service_api.env_manager
    name: env_init
    ip: webarena-env-manager
    port: 1701
  - module_name: webarena.fast_service_api.env_manager
    name: env_step
    ip: webarena-env-manager
    port: 1701
  - module_name: webarena.fast_service_api.env_manager
    name: env_reset
    ip: webarena-env-manager
    port: 1701
  - module_name: webarena.fast_service_api.env_manager
    name: env_evaluate
    ip: webarena-env-manager
    port: 1701
  - module_name: webarena.fast_service_api.env_manager
    name: env_save_trace
    ip: webarena-env-manager
    port: 1701
  - module_name: webarena.fast_service_api.env_manager
    name: env_destroy
    ip: webarena-env-manager
    port: 1701


monitor: True
monitor_storage:
  type: csv
  store_dir: ./.cache/webarena/module/env_manager/client