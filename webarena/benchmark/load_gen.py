"""Script to run end-to-end evaluation on the benchmark"""

import argparse
import json
import logging
import os
import random
import time
from concurrent.futures import Thr<PERSON>PoolExecutor
from pathlib import Path
from typing import List, Optional

from fast_service import (
    FastServiceConfig,
    FastServiceBenchmark,
    FastServiceBenchmarkConfig,
    FastServiceModuleBenchmark,
    FastServiceModuleBenchmarkConfig,
)

from webarena.fast_service_manager import WEBARENA_FSM

from webarena.fast_service_api.execution_coordinator import (
    e2e_execution_with_evaluation,
    e2e_execution_no_evaluation,
)
from webarena.execution_coordinator import E2eExecutionArgs

LOG_FOLDER = "log_files"
Path(LOG_FOLDER).mkdir(parents=True, exist_ok=True)
LOG_FILE_NAME = f"{LOG_FOLDER}/log_{time.strftime('%Y%m%d%H%M%S', time.localtime())}_{random.randint(0, 10000)}.log"

logger = logging.getLogger("logger")
logger.handlers.clear()
logger.setLevel(logging.INFO)

console_handler = logging.StreamHandler()
console_handler.setLevel(logging.DEBUG)
logger.addHandler(console_handler)

file_handler = logging.FileHandler(LOG_FILE_NAME)
file_handler.setLevel(logging.DEBUG)
logger.addHandler(file_handler)

# Set the log format
formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
console_handler.setFormatter(formatter)
file_handler.setFormatter(formatter)


def config() -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        description="Run end-to-end evaluation on the benchmark"
    )
    parser.add_argument("--render", action="store_true", help="Render the browser")
    parser.add_argument(
        "--slow_mo",
        type=int,
        default=0,
        help="Slow down the browser by the specified amount",
    )
    parser.add_argument(
        "--action_set_tag", default="id_accessibility_tree", help="Action type"
    )
    parser.add_argument(
        "--observation_type",
        choices=["accessibility_tree", "html", "image"],
        default="accessibility_tree",
        help="Observation type",
    )
    parser.add_argument(
        "--current_viewport_only",
        action="store_true",
        help="Only use the current viewport for the observation",
    )
    parser.add_argument("--viewport_width", type=int, default=1280)
    parser.add_argument("--viewport_height", type=int, default=720)
    parser.add_argument("--save_trace_enabled", action="store_true")
    parser.add_argument("--sleep_after_execution", type=float, default=0.0)

    parser.add_argument("--max_steps", type=int, default=30)

    # agent config
    parser.add_argument("--agent_type", type=str, default="prompt")
    parser.add_argument(
        "--instruction_path",
        type=str,
        default="config/prompts/state_action_agent.json",
    )
    parser.add_argument(
        "--parsing_failure_th",
        help="When concesecutive parsing failure exceeds this threshold, the agent will stop",
        type=int,
        default=3,
    )
    parser.add_argument(
        "--repeating_action_failure_th",
        help="When concesecutive repeating action exceeds this threshold, the agent will stop",
        type=int,
        default=3,
    )

    # lm config
    parser.add_argument("--provider", type=str, default="openai")
    parser.add_argument("--model", type=str, default="gpt-3.5-turbo-0613")
    parser.add_argument("--mode", type=str, default="completion")
    parser.add_argument("--temperature", type=float, default=1.0)
    parser.add_argument("--top_p", type=float, default=0.9)
    parser.add_argument("--context_length", type=int, default=0)
    parser.add_argument("--max_tokens", type=int, default=4096)
    parser.add_argument("--stop_token", type=str, default=None)
    parser.add_argument(
        "--max_retry",
        type=int,
        help="max retry times to perform generations when parsing fails",
        default=1,
    )
    parser.add_argument(
        "--max_obs_length",
        type=int,
        help="when not zero, will truncate the observation to this length before feeding to the model",
        default=1920,
    )
    parser.add_argument(
        "--model_endpoint",
        help="huggingface model endpoint",
        type=str,
        default="",
    )

    # benchmark loader
    parser.add_argument("--bm_file_path", default="./.cache/webarena_data.txt")
    parser.add_argument("--bm_mode", default="one-by-one")
    parser.add_argument("--bm_request_num", default=100, type=int)
    parser.add_argument("--bm_module", type=str, default=None)
    parser.add_argument("--bm_function", type=str, default=None)
    parser.add_argument("--bm_intermediate_storage", type=str, default=None)

    # logging related
    parser.add_argument("--result_dir", type=str, default="results")

    # executing without evaluation
    parser.add_argument("--no_eval", action="store_true")

    args = parser.parse_args()

    # check the whether the action space is compatible with the observation space
    if (
        args.action_set_tag == "id_accessibility_tree"
        and args.observation_type != "accessibility_tree"
    ):
        raise ValueError(
            f"Action type {args.action_set_tag} is incompatible with the observation type {args.observation_type}"
        )

    return args


def test_all(
    args: argparse.Namespace,
    config_file_list: List[str],
) -> None:
    scores = []

    for config_file in config_file_list:
        test_config_args = E2eExecutionArgs(**vars(args), config_file=config_file)
        score = e2e_execution_with_evaluation(test_config_args).score
        scores.append(score)

    logger.info(f"Average score: {sum(scores) / len(scores)}")


def test_all_parallel(
    args: argparse.Namespace,
    config_file_list: List[str],
    max_workers: Optional[int] = None,
) -> None:
    scores = []

    def call_e2e_execution_no_evaluation(config_file):
        test_config_args = E2eExecutionArgs(**vars(args), config_file=config_file)
        return e2e_execution_no_evaluation(test_config_args).score

    # Use ThreadPoolExecutor to parallelize the function calls
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        scores = list(executor.map(call_e2e_execution_no_evaluation, config_file_list))

    logger.info(f"Average score: {sum(scores) / len(scores)}")


def prepare(args: argparse.Namespace) -> None:
    # convert prompt python files to json
    from webarena.agent.prompts import to_json

    to_json.run()

    # prepare result dir
    result_dir = args.result_dir
    if not result_dir:
        result_dir = f"cache/results_{time.strftime('%Y%m%d%H%M%S', time.localtime())}"
    if not Path(result_dir).exists():
        Path(result_dir).mkdir(parents=True, exist_ok=True)
        args.result_dir = result_dir
        logger.info(f"Create result dir: {result_dir}")

    if not (Path(result_dir) / "traces").exists():
        (Path(result_dir) / "traces").mkdir(parents=True)

    # log the log file
    with open(os.path.join(result_dir, "log_files.txt"), "a+") as f:
        f.write(f"{LOG_FILE_NAME}\n")


def dump_config(args: argparse.Namespace) -> None:
    config_file = Path(args.result_dir) / "config.json"
    if not config_file.exists():
        with open(config_file, "w") as f:
            json.dump(vars(args), f, indent=4)
            logger.info(f"Dump config to {config_file}")


if __name__ == "__main__":
    args = config()
    args.sleep_after_execution = 2.0
    prepare(args)

    args.render = False
    args.render_screenshot = True
    args.save_trace_enabled = True

    args.current_viewport_only = True
    dump_config(args)

    def line_to_req(line: str):
        return E2eExecutionArgs(**vars(args), config_file=line.strip())

    if args.no_eval:

        def invoke_service(request: E2eExecutionArgs):
            return e2e_execution_no_evaluation(request).score

        logger.warning(
            "Evaluation disabled, scores will not be reported. "
            "Remove the --no_eval flag to enable evaluation."
        )
    else:

        def invoke_service(request: E2eExecutionArgs):
            return e2e_execution_with_evaluation(request).score

    fast_service_config = FastServiceConfig.load_from_file("config/client.yml")
    WEBARENA_FSM.setup_client_mode(fast_service_config)

    bm_config = FastServiceBenchmarkConfig(
        file_path=args.bm_file_path,
        mode=args.bm_mode,
        request_num=args.bm_request_num,
    )

    if args.bm_module is not None:
        assert args.bm_function is not None
        assert args.bm_intermediate_storage is not None
        dir_path = os.path.join(
            args.bm_intermediate_storage,
            "requests",
            f"{args.bm_module}.{args.bm_function}",
        )
        module_config = FastServiceModuleBenchmarkConfig(
            module_name=args.bm_module,
            function_name=args.bm_function,
            dir_path=dir_path,
            base_config=bm_config,
        )
        benchmark = FastServiceModuleBenchmark(
            config=module_config, fast_service_manager=WEBARENA_FSM
        )
    else:
        benchmark = FastServiceBenchmark(
            config=bm_config, line_to_request=line_to_req, invoke_service=invoke_service
        )

    logger.info("start benchmark")
    benchmark.execute()
    logger.info("end benchmark")
