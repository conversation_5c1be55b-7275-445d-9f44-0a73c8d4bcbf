"""Script to run end-to-end evaluation on the benchmark"""

import argparse
import json
import logging
import os
import random
import time
from concurrent.futures import Thr<PERSON>PoolExecutor
from pathlib import Path
from typing import List, Optional

from fast_service import (
    FastServiceConfig,
    FastServiceBenchmark,
    FastServiceBenchmarkConfig,
    FastServiceModuleBenchmark,
    FastServiceModuleBenchmarkConfig,
    StatefulModuleBenchmark,
    StatefulModuleBenchmarkConfig,
)

from webarena.fast_service_manager import WEBARENA_FSM

from webarena.fast_service_api.execution_coordinator import (
    e2e_execution_with_evaluation,
    e2e_execution_no_evaluation,
)
from webarena.execution_coordinator import E2eExecutionArgs

LOG_FOLDER = "log_files"
Path(LOG_FOLDER).mkdir(parents=True, exist_ok=True)
LOG_FILE_NAME = f"{LOG_FOLDER}/log_{time.strftime('%Y%m%d%H%M%S', time.localtime())}_{random.randint(0, 10000)}.log"

logger = logging.getLogger("logger")
logger.handlers.clear()
logger.setLevel(logging.INFO)

console_handler = logging.StreamHandler()
console_handler.setLevel(logging.DEBUG)
logger.addHandler(console_handler)

file_handler = logging.FileHandler(LOG_FILE_NAME)
file_handler.setLevel(logging.DEBUG)
logger.addHandler(file_handler)

# Set the log format
formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
console_handler.setFormatter(formatter)
file_handler.setFormatter(formatter)


def config() -> argparse.Namespace:
    parser = argparse.ArgumentParser(description="Run module benchmark")

    # benchmark loader
    parser.add_argument("--bm_config_path", type=str, default=None)
    parser.add_argument("--bm_file_path", default="./.cache/webarena_data.txt")
    parser.add_argument("--bm_mode", default="fixed-interval")
    parser.add_argument("--bm_request_num", default=100, type=int)
    parser.add_argument("--bm_lambda_rate", default=0.01, type=float)
    parser.add_argument("--bm_module", type=str, default=None)
    parser.add_argument(
        "--bm_intermediate_storage", type=str, default=".cache/webarena/intermediate"
    )
    parser.add_argument(
        "--bm_sequence_file_path",
        type=str,
        default=".cache/webarena/monitor/obo/execution_coordinator/remote_delay.csv",
    )

    args = parser.parse_args()

    return args


def prepare(args: argparse.Namespace) -> None:
    # prepare result dir
    result_dir = "results"
    if not result_dir:
        result_dir = f"cache/results_{time.strftime('%Y%m%d%H%M%S', time.localtime())}"
    if not Path(result_dir).exists():
        Path(result_dir).mkdir(parents=True, exist_ok=True)
        logger.info(f"Create result dir: {result_dir}")

    if not (Path(result_dir) / "traces").exists():
        (Path(result_dir) / "traces").mkdir(parents=True)

    # log the log file
    with open(os.path.join(result_dir, "log_files.txt"), "a+") as f:
        f.write(f"{LOG_FILE_NAME}\n")


def dump_config(args: argparse.Namespace) -> None:
    config_file = Path("results") / "config.json"
    if not config_file.exists():
        with open(config_file, "w") as f:
            json.dump(vars(args), f, indent=4)
            logger.info(f"Dump config to {config_file}")


if __name__ == "__main__":
    args = config()
    prepare(args)
    dump_config(args)

    fast_service_config = FastServiceConfig.load_from_file(args.bm_config_path)
    WEBARENA_FSM.setup_client_mode(fast_service_config)

    bm_config = FastServiceBenchmarkConfig(
        file_path=args.bm_file_path,
        mode=args.bm_mode,
        request_num=args.bm_request_num,
        lambda_rate=args.bm_lambda_rate,
    )

    assert args.bm_intermediate_storage is not None
    if args.bm_module == "llm":
        dir_path = os.path.join(
            args.bm_intermediate_storage,
            "llm",
            "requests",
            "webarena.fast_service_api.llm.call_llm",
        )
        module_config = FastServiceModuleBenchmarkConfig(
            module_name="webarena.fast_service_api.llm",
            function_name="call_llm",
            dir_path=dir_path,
            base_config=bm_config,
        )
        benchmark = FastServiceModuleBenchmark(
            config=module_config, fast_service_manager=WEBARENA_FSM
        )
    elif args.bm_module == "env_manager":
        module_config = StatefulModuleBenchmarkConfig(
            module_name="webarena.fast_service_api.env_manager",
            sequence_file_path=args.bm_sequence_file_path,
            dir_path=os.path.join(
                args.bm_intermediate_storage,
                "env_manager",
                "requests",
            ),
            base_config=bm_config,
            init_function="env_init",
            context_id_param="env_id",
        )
        benchmark = StatefulModuleBenchmark(
            config=module_config, fast_service_manager=WEBARENA_FSM
        )
    else:
        raise ValueError(f"{args.bm_module} is not supported")

    logger.info("start benchmark")
    benchmark.execute()
    logger.info("end benchmark")
