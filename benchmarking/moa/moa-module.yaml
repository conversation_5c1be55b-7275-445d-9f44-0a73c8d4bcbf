version: "3.8"

services:

  moa-coordinator:
    image: moa:v1
    container_name: moa-${MOA_MODULE}-coordinator
    ports:
      - "${MOA_MODULE_PORT}:20101"
    volumes:
      - "${MOA_HOME}/.cache/module_bm/${MOA_MODULE}/config/:/workspace/moa/config"
      - "${MOA_HOME}/.cache/intermediate:/workspace/moa/.cache/intermediate"
      - "${MOA_HOME}/.cache/intermediate/monitor/${MOA_MODULE}:/workspace/moa/.cache/monitor"
    environment:
      TRACE_LLM_INFERENCE: False
    command: python scripts/launch_service.py --api_key EMPTY
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:20101/docs" ]
      interval: 10s
      start_period: 10s
      timeout: 5s
      retries: 3

  moa-loadgen:
    image: moa:v1
    container_name: moa-${MOA_MODULE}-loadgen
    volumes:
      - "${MOA_HOME}/benchmark/module_load_gen.py:/workspace/moa/benchmark/module_load_gen.py"
      - "${MOA_HOME}/.cache/module_bm/${MOA_MODULE}/config/:/workspace/moa/config/"
      - "${MOA_HOME}/.cache/requests:/workspace/moa/.cache/requests"
      - "${MOA_HOME}/.cache/intermediate:/workspace/moa/.cache/intermediate"
      - "${MOA_HOME}/.cache/intermediate/monitor/${MOA_MODULE}:/workspace/moa/.cache/monitor"
    depends_on:
      moa-coordinator:
        condition: service_healthy
    command:
      - python
      - benchmark/module_load_gen.py
      - -n
      - "500"
      - -m
      - "fixed-interval"
      - -l
      - "1.0"
      - -fn
      - ${MOA_MODULE}

