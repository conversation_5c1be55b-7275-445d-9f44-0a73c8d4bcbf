version: "3.8"

services:

  moa-coordinator:
    image: moa:v1
    container_name: moa-coordinator
    ports:
      - "20101:20101"
    volumes:
      - "${MOA_HOME}/config:/workspace/moa/config"
      - "${MOA_HOME}/.cache/monitor:/workspace/moa/.cache/monitor"
      - "${MOA_HOME}/.cache/intermediate:/workspace/moa/.cache/intermediate"
    environment:
      TRACE_LLM_INFERENCE: False
    command: python scripts/launch_service.py --api_key EMPTY
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:20101/docs" ]
      interval: 10s
      start_period: 10s
      timeout: 5s
      retries: 3

  moa-loadgen:
    image: moa:v1
    container_name: moa-loadgen
    volumes:
      - "${MOA_HOME}/benchmark/load_gen.py:/workspace/moa/benchmark/load_gen.py"
      - "${MOA_HOME}/config/:/workspace/moa/config/"
      - "${MOA_HOME}/.cache/monitor:/workspace/moa/.cache/monitor"
      - "${MOA_HOME}/.cache/requests:/workspace/moa/.cache/requests"
    depends_on:
      moa-coordinator:
        condition: service_healthy
    command:
      - python
      - benchmark/load_gen.py
      - -n
      - "1000"
      - -m
      - "one-by-one"
      #- -l
      #- "2.0"

