version: "3.8"

services:

  hugginggpt-models-server:
    image: hugginggpt_models_server:v1
    container_name: hugginggpt-models_server
    ports:
      - "7070:7070"
    volumes:
      - "${HUGGINGGPT_HOME}/config/models_server_config.yaml:/workspace/hugginggpt/config/models_server_config.yaml"
      - "${HUGGINGGPT_HOME}/.cache/images:/workspace/hugginggpt/.cache/images" # request images
      - "${HUGGINGGPT_HOME}/.cache/public:/workspace/hugginggpt/public/" # local dir storing responded resources
      - "${HUGGINGGPT_HOME}/.cache/intermediate/monitor/${HUGGINGGPT_MODULE}:/workspace/hugginggpt/src/models_server/.cache/monitor" # monitor data
      - "${HUGGINGFACE_HOME}:/mnt/data/huggingface" # local dir storing huggingface models
    command:
      - python
      - models_server.py
      - -f
      - "/workspace/hugginggpt/config/models_server_config.yaml"
      - --save_dir
      - "/workspace/hugginggpt/public/images"
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:7070/docs" ]
      interval: 30s
      start_period: 30s
      timeout: 5s
      retries: 10

  hugginggpt-coordinator:
    image: hugginggpt:v1
    container_name: hugginggpt-coordinator
    ports:
      - "20101:20101"
    volumes:
      - "${HUGGINGGPT_HOME}/config:/workspace/hugginggpt/config"
      - "${HUGGINGGPT_HOME}/.cache/intermediate/monitor/${HUGGINGGPT_MODULE}:/workspace/hugginggpt/.cache/monitor"
      - "${HUGGINGGPT_HOME}/.cache/public/:/workspace/hugginggpt/public/" # local dir storing responded resources
      - "${HUGGINGGPT_HOME}/.cache/images:/workspace/hugginggpt/.cache/images"
    environment:
      TRACE_LLM_INFERENCE: False
    command: python scripts/launch_services.py
    depends_on:
      hugginggpt-models-server:
        condition: service_healthy
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:20101/docs" ]
      interval: 10s
      start_period: 10s
      timeout: 5s
      retries: 3

  hugginggpt-loadgen:
    image: hugginggpt:v1
    container_name: hugginggpt-loadgen
    volumes:
      - "${HUGGINGGPT_HOME}/benchmark/module_load_gen.py:/workspace/hugginggpt/benchmark/module_load_gen.py"
      - "${HUGGINGGPT_HOME}/config:/workspace/hugginggpt/config"
      - "${HUGGINGGPT_HOME}/.cache/requests:/workspace/hugginggpt/.cache/requests"
      - "${HUGGINGGPT_HOME}/.cache/intermediate:/workspace/hugginggpt/.cache/intermediate"
      - "${HUGGINGGPT_HOME}/.cache/intermediate/monitor/${HUGGINGGPT_MODULE}:/workspace/hugginggpt/.cache/monitor"
    depends_on:
      hugginggpt-coordinator:
        condition: service_healthy
    command:
      - python
      - benchmark/module_load_gen.py
      - -n
      - "1000"
      - -fn
      - ${HUGGINGGPT_MODULE}
      - -m
      - "one-by-one"
      #- -l
      #- "2.0"
      - -d
      - "./.cache/intermediate"

