version: "2.28.1"

services:
  loadgen:
    image: rag:v1
    container_name: loadgen
    deploy:
      resources:
        limits:
          cpus: ${LOADGEN_CPUS:-32.0} # Adjust CPU if necessary
          memory: ${LOADGEN_MEMORY:-64G} # Adjust memory if necessary
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY:-EMPTY}
      - TIKTOKEN_CACHE_DIR=/cache/tiktok
    volumes:
      - ${AGENT_DATA}:${AGENT_DATA}
      - ${AGENT_CACHE}:/cache
      - ${AGENT_CONFIG}:/config
      - ${AGENT_RESULTS}/client:/output
    working_dir: /workspace/rag/benchmark
    command: "echo 'You must provide a command to run in the container'"
