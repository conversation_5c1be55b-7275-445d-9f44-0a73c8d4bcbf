version: "2.28.1"

services:
  coordinator-service:
    image: rag:v1
    container_name: coordinator-service
    deploy:
      resources:
        limits:
          cpus: ${AGENT_CPUS:-4.0} # Adjust CPU if necessary
          memory: ${AGENT_MEMORY:-16G} # Adjust memory if necessary
    volumes:
      - ${AGENT_DATA}:${AGENT_DATA}
      - ${AGENT_CACHE}:/cache
      - ${AGENT_CONFIG}:/config
      - ${AGENT_RESULTS}/server:/output
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY:-EMPTY}
      - TIKTOKEN_CACHE_DIR=/cache/tiktok
    command:
      - python
      - launch_services.py
      - --config
      - /config/server.yml
      - --settings
      - /config/settings.yml
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:20000/docs"]
      interval: 30s
      start_period: 30s
      timeout: 5s
      retries: 3
