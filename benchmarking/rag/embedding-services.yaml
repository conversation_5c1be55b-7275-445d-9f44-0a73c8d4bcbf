version: "2.28.1"

services:
  tei-embedding-service:
    image: ghcr.io/huggingface/text-embeddings-inference:${EMBEDDING_VERSION:-1.5}
    container_name: embedding-service
    runtime: nvidia
    deploy:
      resources:
        limits:
          cpus: ${EMBEDDING_CPUS:-8.0} # Adjust CPU if necessary
          memory: ${EMBEDDING_MEMORY:-16G} # Adjust memory if necessary
        reservations:
          devices:
            - capabilities: [gpu]
              device_ids: ["${EMBEDDING_DEVICE}"]
    volumes:
      - ${HF_HOME:-/mnt/data/huggingface}/hub:/data
    ports:
      - "${EMBEDDING_PORT:-18000}:80"
    environment:
      - MODEL_ID=${EMBEDDING_MODEL:-Alibaba-NLP/gte-Qwen2-1.5B-instruct}
      - HF_API_TOKEN=${HUGGING_FACE_HUB_TOKEN}
      - MAX_CLIENT_BATCH_SIZE=${MAX_CLIENT_BATCH_SIZE:-65536}
      - PAYLOAD_LIMIT=${PAYLOAD_LIMIT:-**********}
      # - MAX_BATCH_TOKENS=${MAX_BATCH_TOKENS:-524288}
      # - MAX_CONCURRENT_REQUESTS=${MAX_CONCURRENT_REQUESTS:-1024}
      # - MAX_BATCH_TOKENS=${MAX_BATCH_TOKENS:-5242880}
      # - MAX_CONCURRENT_REQUESTS=${MAX_CONCURRENT_REQUESTS:-10240}
    command:
      - --model-id
      - ${EMBEDDING_MODEL:-Alibaba-NLP/gte-Qwen2-1.5B-instruct}
      - --hf-api-token
      - ${HUGGING_FACE_HUB_TOKEN}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      start_period: 3600s
      timeout: 20s
      retries: 3

  infinity-embedding-service:
    image: michaelf34/infinity:latest
    container_name: embedding-service
    runtime: nvidia
    deploy:
      resources:
        limits:
          cpus: ${EMBEDDING_CPUS:-8.0} # Adjust CPU if necessary
          memory: ${EMBEDDING_MEMORY:-16G} # Adjust memory if necessary
        reservations:
          devices:
            - capabilities: [gpu]
              device_ids: ["${EMBEDDING_DEVICE}"]
    volumes:
      - ${HF_HOME}:/app/.cache/huggingface
    ports:
      - "${EMBEDDING_PORT}:7070"
    environment:
      - MODEL_ID=${EMBEDDING_MODEL:-Alibaba-NLP/gte-Qwen2-1.5B-instruct}
      - HF_API_TOKEN=${HUGGING_FACE_HUB_TOKEN}
      - INFINITY_BATCH_SIZE=${INFINITY_BATCH_SIZE:-32}
    command:
      - v2
      - --model-id
      - ${EMBEDDING_MODEL:-Alibaba-NLP/gte-Qwen2-1.5B-instruct}
      - --port
      - "7070"
      - --batch-size
      - ${INFINITY_BATCH_SIZE:-32}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7070/health"]
      interval: 30s
      start_period: 3600s
      timeout: 20s
      retries: 3

  vllm-embedding-service:
    image: vllm/vllm-openai:v0.7.2
    container_name: embedding-service
    runtime: nvidia
    deploy:
      resources:
        limits:
          cpus: ${EMBEDDING_CPUS:-8.0} # Adjust CPU if necessary
          memory: ${EMBEDDING_MEMORY:-16G} # Adjust memory if necessary
        reservations:
          devices:
            - driver: nvidia
              device_ids: ["${EMBEDDING_DEVICE}"]
              capabilities: [gpu]
    environment:
      - HUGGING_FACE_HUB_TOKEN=${HUGGING_FACE_HUB_TOKEN}
      - GPU_MEMORY_UTILIZATION=${EMBEDDING_GPU_MEMORY_UTILIZATION:-0.9}
    ports:
      - "${EMBEDDING_PORT:-18000}:8000"
    volumes:
      - ${HF_HOME}:/root/.cache/huggingface
    ipc: host
    command: "--trust-remote-code --task embedding --model ${EMBEDDING_MODEL:-Alibaba-NLP/gte-Qwen2-1.5B-instruct} --gpu-memory-utilization ${EMBEDDING_GPU_MEMORY_UTILIZATION:-0.9} --disable-log-requests"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      start_period: 3600s
      timeout: 20s
      retries: 3
