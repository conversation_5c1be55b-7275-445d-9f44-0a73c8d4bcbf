version: "3.5"

services:
  milvus-service:
    container_name: milvus-service
    image: milvusdb/milvus:v2.5.3
    command: ["milvus", "run", "standalone", "1>", " /dev/null"]
    security_opt:
      - seccomp:unconfined
    environment:
      ETCD_USE_EMBED: true
      ETCD_DATA_DIR: /var/lib/milvus/etcd
      ETCD_CONFIG_PATH: /milvus/configs/embedEtcd.yaml
      COMMON_STORAGETYPE: local
    volumes:
      - milvus-data:/var/lib/milvus
      - ${MILVUS_ETCD_CONFIG:-./.cache/milvus/embedEtcd.yaml}:/milvus/configs/embedEtcd.yaml
      - ${MILVUS_USER_CONFIG:-./.cache/milvus/user.yaml}:/milvus/configs/embedEtcd.yaml
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9091/healthz"]
      interval: 30s
      start_period: 90s
      timeout: 20s
      retries: 3
    ports:
      - "19530:19530"
      - "9091:9091"
    deploy:
      resources:
        limits:
          cpus: ${MILVUS_CPUS:-32.0} # Adjust CPU if necessary
          memory: ${MILVUS_MEMORY:-64G} # Adjust memory if necessary

volumes:
  milvus-data:
