SCRIPT_DIR=$(dirname "$(realpath "$0")")
HUGGINGGPT_DIR=$(dirname "$SCRIPT_DIR")

docker run --rm -it \
	-v ${HUGGINGGPT_DIR}/.cache/images:/workspace/hugginggpt/.cache/images \
	-v ${HUGGINGGPT_DIR}/.cache/requests:/workspace/hugginggpt/.cache/requests \
	-v ${HUGGINGGPT_DIR}/benchmark/prepare_data.py:/workspace/hugginggpt/benchmark/prepare_data.py \
	hugginggpt:v1 python benchmark/prepare_data.py "$@"