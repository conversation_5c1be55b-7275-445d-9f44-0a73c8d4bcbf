# AGENT_COMMONS_HOME=""
# FAST_SERVICE_HOME=""

SCRIPT_DIR=$(dirname "$(realpath "$0")")
HUGGINGGPT_DIR=$(dirname "$SCRIPT_DIR")


AGENT_COMMONS_COPY_DIR=${HUGGINGGPT_DIR}/coordinator/.cache/agent-commons

if [[ ! -d "${AGENT_COMMONS_COPY_DIR}" ]]; then
  if [[ -z "$AGENT_COMMONS_HOME" ]]; then
    echo "AGENT_COMMONS_HOME should be set for lib copy."
  else
    mkdir -p ${AGENT_COMMONS_COPY_DIR}
    cp -r ${AGENT_COMMONS_HOME}/* ${AGENT_COMMONS_COPY_DIR}
  fi
fi

FAST_SERVICE_COPY_DIR=${HUGGINGGPT_DIR}/coordinator/.cache/fast-service
if [[ ! -d "${FAST_SERVICE_COPY_DIR}" ]]; then
  if [[ -z "$FAST_SERVICE_HOME" ]]; then
    echo "FAST_SERVICE_HOME should be set for lib copy."
  else
    mkdir -p ${FAST_SERVICE_COPY_DIR}
    cp -r ${FAST_SERVICE_HOME}/* ${FAST_SERVICE_COPY_DIR}
  fi
fi


cd ${HUGGINGGPT_DIR}/coordinator
docker build -t hugginggpt:v1 .

cd ${HUGGINGGPT_DIR}/models_server
docker build -t hugginggpt_models_server:v1 .