SCRIPT_DIR=$(dirname "$(realpath "$0")")
HUGGINGGPT_DIR=$(dirname "$SCRIPT_DIR")

HUGGINGFACE_DIR=/mnt/data/huggingface

export HUGGINGGPT_HOME=${HUGGINGGPT_DIR}
export HUG<PERSON>NGFACE_HOME=${HUGGINGFACE_DIR}

MODULE=$2

# task_planning, model_selection, model_inference, response_generation
if [[ -z ${MODULE} ]]; then
  export HUGGINGGPT_MODULE=task_planning
else
  export HUGGINGGPT_MODULE=${MODULE}
fi

COMMAND=$1

if [[ ${COMMAND} = "up" ]]; then
  docker compose -f ${HUGGINGGPT_DIR}/../benchmarking/hugginggpt/hugginggpt-module.yaml up -d
else
  docker compose -f ${HUGGINGGPT_DIR}/../benchmarking/hugginggpt/hugginggpt-module.yaml down
fi


