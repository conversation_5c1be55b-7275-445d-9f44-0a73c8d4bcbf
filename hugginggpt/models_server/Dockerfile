FROM pytorch/pytorch:2.5.1-cuda12.4-cudnn9-devel

RUN apt-get update && \
    apt-get install -y curl


# install dependencies for hugginggpt
WORKDIR /workspace/hugginggpt
COPY requirements.txt requirements.txt
RUN pip install -r requirements.txt

# copy necessary code to docker image
COPY ./src /workspace/hugginggpt/src/models_server


# set PYTHONPATH and HF_HOME
ENV PYTHONPATH=/workspace/hugginggpt/src/models_server
ENV HF_HOME=/mnt/data/huggingface

# set workdir and default command
WORKDIR /workspace/hugginggpt/src/models_server
CMD ["python", "models_server.py"]