import os
import sys
import argparse

from fast_service import (
    FastServiceConfig,
    FastServiceBenchmarkConfig,
    FastServiceModuleBenchmarkConfig,
    FastServiceModuleBenchmark,
)

abs_path = os.path.abspath(f"{os.environ.get("HUGGINGGPT_HOME")}/src/hugginggpt")

module_dir = os.path.dirname(abs_path)
if module_dir not in sys.path:
    sys.path.append(module_dir)

from hugginggpt import hugginggpt_fsm


function_dir = {
    "task_planning": "hugginggpt.task_planning",
    "model_selection": "hugginggpt.model_selection",
    "model_inference": "hugginggpt.model_inference",
    "response_generation": "hugginggpt.response_generation",
}

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "-d",
        "--dir_path",
        default=f"{os.environ.get("HUGGINGGPT_HOME")}/.cache/intermediate/",
    )
    parser.add_argument(
        "--config_file",
        default=f"{os.environ.get("HUGGINGGPT_HOME")}/config/hugginggpt_client.yml",
    )
    parser.add_argument("-m", "--mode", default="poisson")
    parser.add_argument("-n", "--request_num", default=1000, type=int)
    parser.add_argument("-l", "--lambda_rate", default=1.0, type=float)
    parser.add_argument("-p", "--task_plan", action="store_true")
    parser.add_argument(
        "-fn", "--function_name", choices=function_dir.keys(), required=True
    )
    parser.add_argument("-s", "--seed", default=0, type=int)
    args = parser.parse_args()

    config_file = args.config_file

    config = FastServiceConfig.load_from_file(os.path.abspath(config_file))
    hugginggpt_fsm.setup_client_mode(config)

    print("start module benchmark")
    base_config = FastServiceBenchmarkConfig(
        mode=args.mode,
        request_num=args.request_num,
        lambda_rate=args.lambda_rate,
    )
    module_config = FastServiceModuleBenchmarkConfig(
        module_name=function_dir[args.function_name],
        function_name=args.function_name,
        dir_path=os.path.join(
            args.dir_path,
            "requests",
            f"{function_dir[args.function_name]}.{args.function_name}",
        ),
        base_config=base_config,
    )
    module_benchmark = FastServiceModuleBenchmark(
        config=module_config, fast_service_manager=hugginggpt_fsm
    )
    module_benchmark.execute()
    print("finish module benchmark")
