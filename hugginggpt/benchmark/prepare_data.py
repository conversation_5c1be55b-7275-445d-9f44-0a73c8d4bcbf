import os
import datasets
import argparse
import urllib.request
import json
from tqdm import tqdm
import tarfile
from huggingface_hub import hf_hub_download
from glob import glob
from PIL import Image


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("-d", "--dir", default="./.cache")
    parser.add_argument("-n", "--num", default=1000, type=int)
    parser.add_argument("-l", "--local", action="store_true")
    parser.add_argument("-t", "--task", default="hybrid-image-text")

    args = parser.parse_args()

    dir_path = args.dir
    req_path = os.path.join(dir_path, "requests")
    image_dir = os.path.join(dir_path, "images")

    if not os.path.exists(req_path):
        os.mkdir(req_path)
    if not os.path.exists(image_dir):
        os.mkdir(image_dir)

    num = args.num

    if args.task == "image-to-text":
        raw_dataset = datasets.load_dataset("Maysee/tiny-imagenet", split="valid")

        # save images
        for i, image in enumerate(tqdm(raw_dataset, total=num, desc="Saving images")):
            if i >= num:
                break
            image = image["image"]
            image_path = os.path.join(image_dir, f"{i}.jpg")
            image.save(image_path)

        file_path = os.path.join(req_path, "hugginggpt_imagetotext_data.txt")
        with open(file_path, "w", encoding="utf-8") as file:
            if args.local:
                for i in range(num):
                    image_url = os.path.abspath(os.path.join(image_dir, f"{i}.jpg"))
                    file.write(f"12, Please talk about the figure {image_url}\n")
            else:
                for i in range(num):
                    image_url = f"http://127.0.0.1:8000/{i}.jpg"
                    file.write(f"12, Please talk about the figure {image_url}\n")

    elif args.task == "text-generation":
        raw_dataset = datasets.load_dataset("tatsu-lab/alpaca")["train"]["instruction"]

        file_path = os.path.join(req_path, "hugginggpt_data.txt")

        with open(file_path, "w", encoding="utf-8") as file:
            for instruction in raw_dataset:
                if "\n" in instruction:
                    continue
                file.write(f"6, {instruction}\n")

    elif args.task == "hybrid-image-text":
        # prepare image
        if not os.path.exists(image_dir):
            os.mkdir(image_dir)

        file_path = hf_hub_download(
            repo_id="jackyhate/text-to-image-2M",
            filename="data_1024_10K/data_000000.tar",
            repo_type="dataset",
            local_dir=".",
            local_dir_use_symlinks=False,
        )

        tar_path = "data_1024_10K/data_000000.tar"
        extract_path = ".cache/images"
        for root, dirs, files in os.walk(extract_path):
            for file in files:
                file_path = os.path.join(root, file)
                os.chmod(file_path, 0o777)

        with tarfile.open(tar_path, "r") as tar:
            tar.extractall(path=extract_path)
        print(f"File extract to: {extract_path}")

        image_names = sorted(glob(os.path.join(extract_path, "*.jpg")))[: args.num]
        for image_name in image_names:
            png_name = image_name.replace(".jpg", ".png")
            im = Image.open(image_name)
            im.save(png_name)

        image_names = [
            os.path.basename(file).replace(".jpg", ".png") for file in image_names
        ]

        # download user_requests template
        def download_file(url, save_path):
            if os.path.exists(save_path):
                return

            try:
                urllib.request.urlretrieve(url, save_path)
                print(f"Successfully download file: {save_path}")
            except Exception as e:
                print(f"Download error: {e}")

        url = "https://raw.githubusercontent.com/microsoft/JARVIS/refs/heads/main/taskbench/data_huggingface/user_requests.json"
        download_file(url, os.path.join(req_path, "user_requests.json"))

        result = []
        image_index = 0
        count = 0
        with open(
            os.path.join(req_path, "user_requests.json"), "r", encoding="utf-8"
        ) as template_file:
            while count < num:
                r = template_file.readline()
                if not r:
                    break

                r = r.strip()
                json_data = json.loads(r)
                reqeust_str: str = json_data["user_request"]

                invalid_keywords = ["audio", "video", "speech"]
                valid = True
                for keyword in invalid_keywords:
                    if keyword in reqeust_str:
                        valid = False
                        break

                if not valid:
                    continue

                valid_keywords = ["example.jpg", "example.png"]
                index = reqeust_str.find("example.")
                replace_strs = []
                while index != -1:
                    cursor = index + len("example.")
                    while cursor < len(reqeust_str):
                        char = reqeust_str[cursor]
                        if char.isalpha() or char.isdigit():
                            cursor += 1
                        else:
                            break
                    space_index = cursor
                    example_str = reqeust_str[index:space_index]

                    if example_str not in valid_keywords:
                        valid = False
                        break

                    replace_strs.append(example_str)

                    index = reqeust_str.find("example.", space_index)

                if not valid:
                    continue

                processed_str = reqeust_str
                for replace_str in replace_strs:
                    image_url = os.path.abspath(
                        os.path.join(image_dir, f"{image_names[image_index]}")
                    )
                    image_index += 1
                    if image_index == num:
                        image_index = 0

                    processed_str = processed_str.replace(replace_str, image_url)

                result.append(processed_str)

                count += 1

        file_path = os.path.join(req_path, "hugginggpt_hybrid_image_text_data.txt")
        with open(file_path, "w", encoding="utf-8") as file:
            for r in result:
                file.write(f"{r}\n")
