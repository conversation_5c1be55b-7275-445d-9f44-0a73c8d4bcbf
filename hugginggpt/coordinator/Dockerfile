FROM python:3.12

# download and install fast-service
COPY .cache/fast-service /workspace/fast-service
RUN cd /workspace/fast-service && \
    pip install -r requirements.txt && \
    pip install -e .

COPY .cache/agent-commons /workspace/agent-commons
RUN cd /workspace/agent-commons && \
    pip install -r requirements.txt && \
    pip install -e .

# install dependencies for hugginggpt
WORKDIR /workspace/hugginggpt
COPY requirements.txt requirements.txt
#RUN pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
RUN pip install -r requirements.txt


# copy necessary code to docker image
COPY src src
COPY scripts scripts
ENV HUGGINGGPT_HOME=/workspace/hugginggpt
WORKDIR /workspace/hugginggpt

