import os
import copy
from typing import List, Dict
from pandas import DataFrame
import hashlib
import json

from fast_service.analysis import (
    DelayRecord,
    DelayFile,
)


class ExecutionNode:

    call_id: str
    parent_call_id: str
    source: str
    task_name: str
    start_time: float
    end_time: float
    delay: float
    delay_type: str

    parent: "ExecutionNode"
    children_list: list["ExecutionNode"]

    def __init__(
        self,
        record: DelayRecord,
        source: str,
        delay_type: str = None,
        parent: "ExecutionNode" = None,
    ):
        self.call_id = record.call_id
        self.parent_call_id = record.parent_call_id
        self.source = source
        self.task_name = record.function_name
        self.start_time = record.end_time - record.delay
        self.end_time = record.end_time
        self.delay = record.delay
        self.delay_type = delay_type

        self.parent = parent
        self.children_list = []

    def add_child(self, child: "ExecutionNode"):
        updated = False
        for i in range(len(self.children_list)):
            if self.children_list[i].start_time > child.start_time:
                self.children_list.insert(i, child)
                updated = True
                break
        if not updated:
            self.children_list.append(child)
        child.parent = self

    def is_parallel_invocation(self) -> bool:
        for i in range(len(self.children_list) - 1):
            if self.children_list[i].end_time > self.children_list[i + 1].start_time:
                return True
        return False


class ExecutionTree:

    root: ExecutionNode

    def __init__(self, root: ExecutionNode):
        self.root = root

    def equals(
        self,
        other: "ExecutionTree",
        excluded_tasks: list[str] = None,
    ) -> bool:

        def compare_sub_tree(
            this_root: ExecutionNode,
            that_root: ExecutionNode,
        ) -> bool:
            if this_root.task_name != that_root.task_name:
                return False

            if excluded_tasks and this_root.task_name in excluded_tasks:
                return True

            if len(this_root.children_list) != len(that_root.children_list):
                return False

            for i in range(len(this_root.children_list)):
                if not compare_sub_tree(
                    this_root.children_list[i], that_root.children_list[i]
                ):
                    return False

            return True

        return compare_sub_tree(self.root, other.root)

    def get_all_tasks(self, unique: bool = True) -> list[str]:
        tasks = []

        def traverse(node: ExecutionNode):
            if node.task_name not in tasks:
                tasks.append(node.task_name)
            for child in node.children_list:
                traverse(child)

        traverse(self.root)
        if unique:
            return list(set(tasks))
        else:
            return tasks

    def duplicate(self, exclude_tasks: list[str] = None) -> "ExecutionTree":
        def _duplicate(node: ExecutionNode) -> ExecutionNode:
            if exclude_tasks and node.task_name in exclude_tasks:
                return None
            new_node = copy.copy(node)
            new_node.children_list = []
            new_node.parent = None  # Reset parent to avoid circular references
            for child in node.children_list:
                duplicated_child = _duplicate(child)
                if duplicated_child:
                    new_node.add_child(duplicated_child)
            return new_node

        new_root = _duplicate(self.root)
        return ExecutionTree(new_root) if new_root else None

    def get_structure(
        self,
        indent: int = 0,
        order_by: str = "start_time",
        exclude_tasks: list[str] = None,
        nick_name_map: dict[str, str] = None,  # used for shortening task names
    ) -> str:
        new_tree = self.duplicate(exclude_tasks)

        def _get_structure(
            node: ExecutionNode, indent: int, order_by: str, exclude_tasks: list[str]
        ) -> str:
            if exclude_tasks and node.task_name in exclude_tasks:
                return ""
            nick_name = node.task_name
            if nick_name_map and node.task_name in nick_name_map:
                nick_name = nick_name_map[node.task_name]
            structure = " " * indent + f"- {nick_name} ({node.source})\n"
            if order_by == "structure":
                sub_structures = [
                    _get_structure(child, indent + 2, order_by, exclude_tasks)
                    for child in node.children_list
                ]
                sub_structures = sorted(sub_structures)
                structure += "".join(sub_structures)
            else:
                for child in sorted(
                    node.children_list, key=lambda x: getattr(x, order_by)
                ):
                    structure += _get_structure(
                        child, indent + 2, order_by, exclude_tasks
                    )
            return structure

        return _get_structure(new_tree.root, indent, order_by, exclude_tasks)


def parse_delay_data(instance_dir_list: list[str], tag: str = "agent_delay"):

    delay_file_list = []
    for instance_dir in instance_dir_list:
        delay_file_list.append(
            DelayFile(
                os.path.basename(instance_dir),
                os.path.join(instance_dir, "remote_delay.csv"),
            )
        )
        delay_file_list.append(
            DelayFile(
                os.path.basename(instance_dir),
                os.path.join(instance_dir, "e2e_delay.csv"),
            )
        )

    request_dict = {}
    execution_node_dict: dict[str, dict[str, ExecutionNode]] = {}
    for delay_file in delay_file_list:
        if "remote_delay.csv" in delay_file.file_path:
            delay_type = "remote"
        elif "e2e_delay.csv" in delay_file.file_path:
            delay_type = "e2e"
        else:
            raise ValueError(f"Unknown delay file type: {delay_file.file_path}")
        for record in delay_file.content:
            if record.request_id not in request_dict:
                request_dict[record.request_id] = []
            request_dict[record.request_id].append(
                [
                    record.call_id,
                    record.parent_call_id,
                    delay_file.instance_name,
                    record.function_name,
                    record.end_time - record.delay,
                    record.end_time,
                    record.delay,
                    delay_type,
                ]
            )

            if record.request_id not in execution_node_dict:
                execution_node_dict[record.request_id] = {}
            execution_node_dict[record.request_id][record.call_id] = ExecutionNode(
                record, delay_file.instance_name, delay_type
            )

    execution_tree_dict = {}
    for request_id, node_dict in execution_node_dict.items():
        candidate_root_list: list[ExecutionNode] = []
        for call_id, node in node_dict.items():
            if node.parent_call_id in node_dict:
                node_dict[node.parent_call_id].add_child(node)
            else:
                candidate_root_list.append(node)
        if len(candidate_root_list) > 1:
            print(
                "WARNING: multiple root nodes found ",
                [
                    f"call {_.call_id} in request {request_id}"
                    for _ in candidate_root_list
                ],
            )
        execution_tree_dict[request_id] = ExecutionTree(candidate_root_list[0])

    columns = [
        "call_id",
        "parent_call_id",
        "source",
        "task_name",
        "start_time",
        "end_time",
        "delay",
        "delay_type",
    ]

    result_df_dict = {}
    for k, v in request_dict.items():
        result_df_dict[k] = DataFrame(v, columns=columns)

    return result_df_dict, execution_tree_dict
