import os
import json


class VLLMRequestRecord:

    request_id: str
    agent_call_id: str

    input_token_num: int
    output_token_num: int
    cached_token_num: int

    request_time: float
    response_time: float
    delay: float

    input_content: str
    output_content: str

    def __init__(
        self,
        request_id: str,
        agent_call_id: str,
        input_token_num: int,
        output_token_num: int,
        cached_token_num: int,
        request_time: float,
        response_time: float,
        delay: float,
        input_content: str,
        output_content: str,
    ):
        self.request_id = request_id
        self.agent_call_id = agent_call_id
        self.input_token_num = input_token_num
        self.output_token_num = output_token_num
        self.cached_token_num = cached_token_num
        self.request_time = request_time
        self.response_time = response_time
        self.delay = delay
        self.input_content = input_content
        self.output_content = output_content


class VLLMRequestParser:

    dir_path_list: list[str]

    def __init__(self, dir_path_list: list[str]):
        self.dir_path_list = dir_path_list

    def parse(self) -> dict[str, VLLMRequestRecord]:

        total_res = {}

        for dir_path in self.dir_path_list:
            for k, v in self._parse_single(dir_path).items():
                total_res[k] = v

        return total_res

    def _parse_single(self, dir_path: str) -> dict[str, VLLMRequestRecord]:
        with (
            open(os.path.join(dir_path, "llm_metadata.txt"), "r") as metadata_file,
            open(os.path.join(dir_path, "llm_chat_request.txt"), "r") as request_file,
            open(os.path.join(dir_path, "llm_chat_response.txt"), "r") as response_file,
        ):
            return self._parse(metadata_file, request_file, response_file)

    def _parse(
        self,
        metadata_file,
        request_file,
        response_file,
    ) -> dict[str, VLLMRequestRecord]:

        metadata_lines = metadata_file.readlines()
        request_lines = request_file.readlines()
        response_lines = response_file.readlines()

        line_num = min(len(metadata_lines), len(request_lines), len(response_lines))

        request_dict = {}

        for i in range(line_num):
            metadata_line = metadata_lines[i]
            request_line = request_lines[i]
            response_line = response_lines[i]
            req = json.loads(request_line)
            resp = json.loads(response_line)

            record = json.loads(metadata_line.strip())
            request_id = record["request_id"]

            request_id_head = "chatcmpl-"

            agent_call_id = request_id[len(request_id_head) :]

            request_time = float(record["request_time"])
            response_time = float(record["response_time"])
            delay = float(record["elapsed_time"])

            usage = record["usage"]

            input_token_num = int(usage["prompt_tokens"])
            output_token_num = int(usage["completion_tokens"])
            if (
                "prompt_tokens_details" in usage
                and usage["prompt_tokens_details"] is not None
                and "cached_tokens" in usage["prompt_tokens_details"]
            ):
                cached_token_num = int(usage["prompt_tokens_details"]["cached_tokens"])
            else:
                cached_token_num = 0

            request_dict[agent_call_id] = VLLMRequestRecord(
                request_id,
                agent_call_id,
                input_token_num,
                output_token_num,
                cached_token_num,
                request_time,
                response_time,
                delay,
                input_content="".join([msg["content"] for msg in req["messages"]]),
                output_content=resp["choices"][0]["message"]["content"],
            )

        return request_dict
