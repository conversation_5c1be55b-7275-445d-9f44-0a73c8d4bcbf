from pandas import Data<PERSON>rame

from fast_service.analysis import DelayRecord, DelayFile

from .vllm_trace import V<PERSON><PERSON>equestParser, VLLMRequestRecord
from .agent_llm_call import parse_agent_llm_call


def parse_llm_call_metrics(
    tag: str, vllm_metrics_dirs: list[str], agent_metrics_files: list[str]
) -> DataFrame:

    vllm_req_parser = VLLMRequestParser(dir_path_list=vllm_metrics_dirs)
    vllm_record_dict: dict[str, VLLMRequestRecord] = vllm_req_parser.parse()

    agent_record_dict: dict[str, DelayRecord] = parse_agent_llm_call(
        delay_file_paths=agent_metrics_files, tag=tag
    )

    columns = [
        "request_id",
        "task_name",
        "prompt_length",
        "generation_length",
        "shared_prompt_length",
        "cache_rate",
        "prompt",
        "generation",
        "request_time",
        "response_time",
    ]

    request_list = []
    for call_id, vllm_record in vllm_record_dict.items():
        agent_record = agent_record_dict[call_id]
        request_list.append(
            [
                agent_record.request_id,
                agent_record.function_name,
                vllm_record.input_token_num,
                vllm_record.output_token_num,
                vllm_record.cached_token_num,
                vllm_record.cached_token_num / vllm_record.input_token_num,
                vllm_record.input_content,
                vllm_record.output_content,
                vllm_record.request_time,
                vllm_record.response_time,
            ]
        )

    return DataFrame(request_list, columns=columns)
