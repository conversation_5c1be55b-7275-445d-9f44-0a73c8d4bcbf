import json
import os
import sys
import msgpack
from tqdm import tqdm
from pandas import DataFrame


def parse_task_io_data(
    task_name: str, dir_path: str, context_name: str = "context"
) -> DataFrame:

    with (
        open(os.path.join(dir_path, "requests.txt"), "r") as request_file,
        open(os.path.join(dir_path, "responses.txt"), "r") as response_file,
    ):
        request_lines = request_file.readlines()
        response_lines = response_file.readlines()

        res = []
        length = min(len(request_lines), len(response_lines))
        for i in tqdm(range(length), desc=f"Processing task {task_name}"):
            request_line = request_lines[i]
            response_line = response_lines[i]

            request_json = json.loads(request_line)
            response_json = json.loads(response_line)

            req_content_json = json.loads(request_json["content"])
            input_dict = {}
            for k, v in req_content_json.items():
                if k == "context":
                    continue
                input_dict[k] = v
            input_str = json.dumps(input_dict).encode("utf-8")
            context_json = req_content_json[context_name]

            res.append(
                [
                    request_json["request_id"],
                    task_name,
                    context_json["call_id"],
                    len(input_str),
                    len(msgpack.packb(input_dict)),
                    len(response_json["content"].encode("utf-8")),
                    len(msgpack.packb(json.loads(response_json["content"]))),
                ]
            )

        columns = [
            "request_id",
            "task_name",
            "call_id",
            "input_size_in_json_str",
            "input_size_in_bytes",
            "output_size_in_json_str",
            "output_size_in_bytes",
        ]

        return DataFrame(res, columns=columns)


def parse_module_io_data(
    dir_path: str, context_name: str = "context"
) -> dict[str, DataFrame]:

    tasks = os.listdir(dir_path)
    res = {}
    for task in tasks:
        res[task] = parse_task_io_data(
            task, os.path.join(dir_path, task), context_name=context_name
        )

    return res


def parse_modules_io_data(
    dir_paths: list[str], context_name: str = "context"
) -> dict[str, DataFrame]:
    res = {}
    for dir_path in dir_paths:
        if not os.path.exists(dir_path):
            raise FileNotFoundError(f"Directory {dir_path} does not exist.")
        tasks = os.listdir(dir_path)
        for task in tasks:
            if task not in res:
                res[task] = parse_task_io_data(
                    task, os.path.join(dir_path, task), context_name=context_name
                )
            else:
                raise ValueError(
                    f"Task {task} already exists in the result dictionary."
                )

    return res
