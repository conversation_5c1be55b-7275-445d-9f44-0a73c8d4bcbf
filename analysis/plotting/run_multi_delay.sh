#!/bin/bash

args="$@"
if [ -z "$args" ]; then
    echo "No additional arguments provided. $args"
else
    echo "Running delay analysis with arguments: $args"
fi

# Hugging GPT Delay Analysis Script
run_hgpt(){
    instance_dirs=(
        /mnt/data/agbench/results/hugginggpt/monitor/obo/hugginggpt_client
        /mnt/data/agbench/results/hugginggpt/monitor/obo/hugginggpt_server
        /mnt/data/agbench/results/hugginggpt/monitor/fix_1/hugginggpt_client
        /mnt/data/agbench/results/hugginggpt/monitor/fix_1/hugginggpt_server
        /mnt/data/agbench/results/hugginggpt/monitor/fix_2/hugginggpt_client
        /mnt/data/agbench/results/hugginggpt/monitor/fix_2/hugginggpt_server
        /mnt/data/agbench/results/hugginggpt/monitor/fix_4/hugginggpt_client
        /mnt/data/agbench/results/hugginggpt/monitor/fix_4/hugginggpt_server
        /mnt/data/agbench/results/hugginggpt/monitor/fix_8/hugginggpt_client
        /mnt/data/agbench/results/hugginggpt/monitor/fix_8/hugginggpt_server
    )
    instance_groups=(
        "bsl"
        "bsl"
        "nthreads=32;rps=1"
        "nthreads=32;rps=1"
        "nthreads=32;rps=2"
        "nthreads=32;rps=2"
        "nthreads=32;rps=4"
        "nthreads=32;rps=4"
        "nthreads=32;rps=8"
        "nthreads=32;rps=8"
    )
    # concatenate instance directories into a single string
    instance_dirs_str=$(printf " %s" "${instance_dirs[@]}")
    # concatenate instance groups into a single string
    instance_groups_str=$(printf " %s" "${instance_groups[@]}")

    python delay.py --tag hgpt \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --exclude_tasks hugginggpt_e2e \
        --include_route \
        --include_delay_types e2e \
        --save_dir ./plots/multi_delay/hgpt/original $args

    python delay.py --tag hgpt \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --exclude_tasks hugginggpt_e2e \
        --include_route \
        --include_delay_types e2e \
        --merge_task \
        --save_dir ./plots/multi_delay/hgpt/merged $args

    python delay.py --tag hgpt-e2e \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --include_sources hugginggpt_client \
        --include_route \
        --save_dir ./plots/multi_delay/hgpt-e2e/original $args

    python delay.py --tag hgpt-e2e \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --include_sources hugginggpt_client \
        --include_route \
        --merge_task \
        --save_dir ./plots/multi_delay/hgpt-e2e/merged $args
}

# Deep Research Delay Analysis Script
run_drs(){
    instance_dirs=(
        /mnt/data/agbench/results/drs/final/baseline/stage-2/drs_async-YDCDRS-OneByOne-0.0-202/results/client/monitor/client
        /mnt/data/agbench/results/drs/final/baseline/stage-2/drs_async-YDCDRS-OneByOne-0.0-202/results/server/monitor/server
        /mnt/data/agbench/results/drs/final/baseline/stage-4/2threads/drs_async-YDCDRS-FixedInterval-2.0-202/results/client/monitor/client
        /mnt/data/agbench/results/drs/final/baseline/stage-4/2threads/drs_async-YDCDRS-FixedInterval-2.0-202/results/server/monitor/server
        /mnt/data/agbench/results/drs/final/baseline/stage-4/4threads/drs_async-YDCDRS-FixedInterval-4.0-202/results/client/monitor/client
        /mnt/data/agbench/results/drs/final/baseline/stage-4/4threads/drs_async-YDCDRS-FixedInterval-4.0-202/results/server/monitor/server
        /mnt/data/agbench/results/drs/final/baseline/stage-4/8threads/drs_async-YDCDRS-FixedInterval-8.0-202/results/client/monitor/client
        /mnt/data/agbench/results/drs/final/baseline/stage-4/8threads/drs_async-YDCDRS-FixedInterval-8.0-202/results/server/monitor/server
        /mnt/data/agbench/results/drs/final/baseline/stage-4/12threads/drs_async-YDCDRS-FixedInterval-12.0-202/results/client/monitor/client
        /mnt/data/agbench/results/drs/final/baseline/stage-4/12threads/drs_async-YDCDRS-FixedInterval-12.0-202/results/server/monitor/server
        /mnt/data/agbench/results/drs/final/baseline/stage-4/16threads/drs_async-YDCDRS-FixedInterval-16.0-202/results/client/monitor/client
        /mnt/data/agbench/results/drs/final/baseline/stage-4/16threads/drs_async-YDCDRS-FixedInterval-16.0-202/results/server/monitor/server
        /mnt/data/agbench/results/drs/final/baseline/stage-4/32threads/drs_async-YDCDRS-FixedInterval-32.0-202/results/client/monitor/client
        /mnt/data/agbench/results/drs/final/baseline/stage-4/32threads/drs_async-YDCDRS-FixedInterval-32.0-202/results/server/monitor/server
        /mnt/data/agbench/results/drs/final/baseline/stage-4/48threads/drs_async-YDCDRS-FixedInterval-48.0-202/results/client/monitor/client
        /mnt/data/agbench/results/drs/final/baseline/stage-4/48threads/drs_async-YDCDRS-FixedInterval-48.0-202/results/server/monitor/server
        /mnt/data/agbench/results/drs/final/baseline/stage-4/64threads/drs_async-YDCDRS-FixedInterval-64.0-202/results/client/monitor/client
        /mnt/data/agbench/results/drs/final/baseline/stage-4/64threads/drs_async-YDCDRS-FixedInterval-64.0-202/results/server/monitor/server
    )
    instance_groups=(
        "bsl"
        "bsl"
        "nthreads=32;rps=2"
        "nthreads=32;rps=2"
        "nthreads=32;rps=4"
        "nthreads=32;rps=4"
        "nthreads=32;rps=8"
        "nthreads=32;rps=8"
        "nthreads=32;rps=12"
        "nthreads=32;rps=12"
        "nthreads=32;rps=16"
        "nthreads=32;rps=16"
        "nthreads=32;rps=32"
        "nthreads=32;rps=32"
        "nthreads=32;rps=48"
        "nthreads=32;rps=48"
        "nthreads=32;rps=64"
        "nthreads=32;rps=64"
    )
    # concatenate instance directories into a single string
    instance_dirs_str=$(printf " %s" "${instance_dirs[@]}")
    # concatenate instance groups into a single string
    instance_groups_str=$(printf " %s" "${instance_groups[@]}")

    python delay.py --tag drs \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --include_tasks with_llm_async search_with_queries_async split_texts_async embed_docs_async store_vdb_async embed_texts_async search_vdb_async clear_vdb_async \
        --exclude_tasks inner_embed_texts_async \
        --exclude_tasks_in_tree summarize \
        --include_route \
        --include_delay_types e2e \
        --save_dir ./plots/multi_delay/drs/original $args

    python delay.py --tag drs \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --include_tasks with_llm_async search_with_queries_async split_texts_async embed_docs_async store_vdb_async embed_texts_async search_vdb_async clear_vdb_async \
        --exclude_tasks inner_embed_texts_async \
        --exclude_tasks_in_tree summarize \
        --include_route \
        --include_delay_types e2e \
        --merge_task \
        --save_dir ./plots/multi_delay/drs/merged $args

    python delay.py --tag drs-e2e \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --include_sources client \
        --include_tasks workflow_async \
        --exclude_tasks_in_tree summarize \
        --include_route \
        --save_dir ./plots/multi_delay/drs-e2e/original $args

    python delay.py --tag drs-e2e \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --include_sources client \
        --include_tasks workflow_async \
        --exclude_tasks_in_tree summarize \
        --include_route \
        --merge_task \
        --save_dir ./plots/multi_delay/drs-e2e/merged $args

}

# SWE Agent Delay Analysis Script
run_swe(){
    instance_dirs=(
        /mnt/data/agbench/results/swe/swe-ds32B/stage-2/data/swe-agent/client
        /mnt/data/agbench/results/swe/swe-ds32B/stage-2/data/swe-agent/model
        /mnt/data/agbench/results/swe/swe-ds32B/stage-2/data/swe-agent/server
        /mnt/data/agbench/results/swe/swe-ds32B/stage-2/data/swe-agent/SWEEnv
        /mnt/data/agbench/results/swe/swe-ds32B/stage-4/2threads/rps2/data/swe-agent/client
        /mnt/data/agbench/results/swe/swe-ds32B/stage-4/2threads/rps2/data/swe-agent/model
        /mnt/data/agbench/results/swe/swe-ds32B/stage-4/2threads/rps2/data/swe-agent/server
        /mnt/data/agbench/results/swe/swe-ds32B/stage-4/2threads/rps2/data/swe-agent/SWEEnv
        /mnt/data/agbench/results/swe/swe-ds32B/stage-4/4threads/rps4/data/swe-agent/client
        /mnt/data/agbench/results/swe/swe-ds32B/stage-4/4threads/rps4/data/swe-agent/model
        /mnt/data/agbench/results/swe/swe-ds32B/stage-4/4threads/rps4/data/swe-agent/server
        /mnt/data/agbench/results/swe/swe-ds32B/stage-4/4threads/rps4/data/swe-agent/SWEEnv
        /mnt/data/agbench/results/swe/swe-ds32B/stage-4/8threads/rps8/data/swe-agent/client
        /mnt/data/agbench/results/swe/swe-ds32B/stage-4/8threads/rps8/data/swe-agent/model
        /mnt/data/agbench/results/swe/swe-ds32B/stage-4/8threads/rps8/data/swe-agent/server
        /mnt/data/agbench/results/swe/swe-ds32B/stage-4/8threads/rps8/data/swe-agent/SWEEnv
    )
    instance_groups=(
        "bsl"
        "bsl"
        "bsl"
        "bsl"
        "nthreads=32;rps=2"
        "nthreads=32;rps=2"
        "nthreads=32;rps=2"
        "nthreads=32;rps=2"
        "nthreads=32;rps=4"
        "nthreads=32;rps=4"
        "nthreads=32;rps=4"
        "nthreads=32;rps=4"
        "nthreads=32;rps=8"
        "nthreads=32;rps=8"
        "nthreads=32;rps=8"
        "nthreads=32;rps=8"
    )
    # concatenate instance directories into a single string
    instance_dirs_str=$(printf " %s" "${instance_dirs[@]}")
    # concatenate instance groups into a single string
    instance_groups_str=$(printf " %s" "${instance_groups[@]}")

    python delay.py --tag swe \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --exclude_tasks swe_agent_request \
        --include_route \
        --include_delay_types e2e \
        --save_dir ./plots/multi_delay/swe/original $args

    python delay.py --tag swe \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --exclude_tasks swe_agent_request \
        --include_route \
        --include_delay_types e2e \
        --merge_task \
        --save_dir ./plots/multi_delay/swe/merged $args

    python delay.py --tag swe-e2e \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --include_sources client \
        --include_route \
        --save_dir ./plots/multi_delay/swe-e2e/original $args

    python delay.py --tag swe-e2e \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --include_sources client \
        --include_route \
        --merge_task \
        --save_dir ./plots/multi_delay/swe-e2e/merged $args
}

# Web Arena Delay Analysis Script
run_webarena(){
    instance_dirs=(
        /mnt/data/agbench/results/webarena/monitor/obo/client
        /mnt/data/agbench/results/webarena/monitor/obo/env_manager
        /mnt/data/agbench/results/webarena/monitor/obo/execution_coordinator
        /mnt/data/agbench/results/webarena/monitor/obo/llm
        /mnt/data/agbench/results/webarena/monitor/fix_0_0_1/client
        /mnt/data/agbench/results/webarena/monitor/fix_0_0_1/env_manager
        /mnt/data/agbench/results/webarena/monitor/fix_0_0_1/execution_coordinator
        /mnt/data/agbench/results/webarena/monitor/fix_0_0_1/llm
        /mnt/data/agbench/results/webarena/monitor/fix_0_0_2/client
        /mnt/data/agbench/results/webarena/monitor/fix_0_0_2/env_manager
        /mnt/data/agbench/results/webarena/monitor/fix_0_0_2/execution_coordinator
        /mnt/data/agbench/results/webarena/monitor/fix_0_0_2/llm
        /mnt/data/agbench/results/webarena/monitor/fix_0_0_4/client
        /mnt/data/agbench/results/webarena/monitor/fix_0_0_4/env_manager
        /mnt/data/agbench/results/webarena/monitor/fix_0_0_4/execution_coordinator
        /mnt/data/agbench/results/webarena/monitor/fix_0_0_4/llm
        /mnt/data/agbench/results/webarena/monitor/fix_0_1/llm
        /mnt/data/agbench/results/webarena/monitor/fix_0_1/client
        /mnt/data/agbench/results/webarena/monitor/fix_0_1/env_manager
        /mnt/data/agbench/results/webarena/monitor/fix_0_1/execution_coordinator
    )
    instance_groups=(
        "bsl"
        "bsl"
        "bsl"
        "bsl"
        "nthreads=32;rps=0.01"
        "nthreads=32;rps=0.01"
        "nthreads=32;rps=0.01"
        "nthreads=32;rps=0.01"
        "nthreads=32;rps=0.02"
        "nthreads=32;rps=0.02"
        "nthreads=32;rps=0.02"
        "nthreads=32;rps=0.02"
        "nthreads=32;rps=0.04"
        "nthreads=32;rps=0.04"
        "nthreads=32;rps=0.04"
        "nthreads=32;rps=0.04"
        "nthreads=32;rps=0.1"
        "nthreads=32;rps=0.1"
        "nthreads=32;rps=0.1"
        "nthreads=32;rps=0.1"
    )
    # concatenate instance directories into a single string
    instance_dirs_str=$(printf " %s" "${instance_dirs[@]}")
    # concatenate instance groups into a single string
    instance_groups_str=$(printf " %s" "${instance_groups[@]}")

    python delay.py --tag webarena \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --exclude_tasks e2e_execution \
        --include_route \
        --include_delay_types e2e \
        --save_dir ./plots/multi_delay/webarena/original $args

    python delay.py --tag webarena \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --exclude_tasks e2e_execution \
        --include_route \
        --include_delay_types e2e \
        --merge_task \
        --save_dir ./plots/multi_delay/webarena/merged $args

    python delay.py --tag webarena-e2e \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --include_sources client \
        --include_route \
        --save_dir ./plots/multi_delay/webarena-e2e/original $args

    python delay.py --tag webarena-e2e \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --include_sources client \
        --include_route \
        --merge_task \
        --save_dir ./plots/multi_delay/webarena-e2e/merged $args
}

# MoA Delay Analysis Script
run_moa(){
    instance_dirs=(
        /mnt/data/agbench/results/moa/monitor/obo/moa_client
        /mnt/data/agbench/results/moa/monitor/obo/moa_service
        /mnt/data/agbench/results/moa/monitor/fix_0_1/moa_client
        /mnt/data/agbench/results/moa/monitor/fix_0_1/moa_service
        /mnt/data/agbench/results/moa/monitor/fix_0_2/moa_client
        /mnt/data/agbench/results/moa/monitor/fix_0_2/moa_service
        /mnt/data/agbench/results/moa/monitor/fix_0_4/moa_client
        /mnt/data/agbench/results/moa/monitor/fix_0_4/moa_service
        /mnt/data/agbench/results/moa/monitor/fix_0_8/moa_client
        /mnt/data/agbench/results/moa/monitor/fix_0_8/moa_service
        /mnt/data/agbench/results/moa/monitor/fix_1/moa_client
        /mnt/data/agbench/results/moa/monitor/fix_1/moa_service
        /mnt/data/agbench/results/moa/monitor/fix_2/moa_client
        /mnt/data/agbench/results/moa/monitor/fix_2/moa_service
        /mnt/data/agbench/results/moa/monitor/fix_4/moa_client
        /mnt/data/agbench/results/moa/monitor/fix_4/moa_service
        /mnt/data/agbench/results/moa/monitor/fix_8/moa_client
        /mnt/data/agbench/results/moa/monitor/fix_8/moa_service
    )
    instance_groups=(
        "bsl"
        "bsl"
        "nthreads=32;rps=0.1"
        "nthreads=32;rps=0.1"
        "nthreads=32;rps=0.2"
        "nthreads=32;rps=0.2"
        "nthreads=32;rps=0.4"
        "nthreads=32;rps=0.4"
        "nthreads=32;rps=0.8"
        "nthreads=32;rps=0.8"
        "nthreads=32;rps=1"
        "nthreads=32;rps=1"
        "nthreads=32;rps=2"
        "nthreads=32;rps=2"
        "nthreads=32;rps=4"
        "nthreads=32;rps=4"
        "nthreads=32;rps=8"
        "nthreads=32;rps=8"
    )
    # concatenate instance directories into a single string
    instance_dirs_str=$(printf " %s" "${instance_dirs[@]}")
    # concatenate instance groups into a single string
    instance_groups_str=$(printf " %s" "${instance_groups[@]}")

    python delay.py --tag moa \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --exclude_tasks moa_chat layer_inference \
        --include_route \
        --include_delay_types e2e \
        --save_dir ./plots/multi_delay/moa/original $args

    python delay.py --tag moa \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --exclude_tasks moa_chat layer_inference \
        --include_route \
        --include_delay_types e2e \
        --merge_task \
        --save_dir ./plots/multi_delay/moa/merged $args

    python delay.py --tag moa-e2e \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --include_sources moa_client \
        --include_route \
        --save_dir ./plots/multi_delay/moa-e2e/original $args

    python delay.py --tag moa-e2e \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --include_sources moa_client \
        --include_route \
        --merge_task \
        --save_dir ./plots/multi_delay/moa-e2e/merged $args
}

run_rag(){
    instance_dirs_str=$1
    instance_groups_str=$2
    rag_name=$3
    python delay.py --tag $rag_name \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --include_tasks retrieval_judger query_translation direct_generation normal_generation load_docs split_docs embed_documents vdb_store embed_query vdb_search reranking \
        --include_route \
        --include_delay_types e2e \
        --save_dir ./plots/multi_delay/$rag_name/original $args

    python delay.py --tag $rag_name \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --include_tasks retrieval_judger query_translation direct_generation normal_generation load_docs split_docs embed_documents vdb_store embed_query vdb_search reranking \
        --include_route \
        --include_delay_types e2e \
        --merge_task \
        --save_dir ./plots/multi_delay/$rag_name/merged $args

    python delay.py --tag $rag_name-e2e \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --include_sources rag_client \
        --include_route \
        --save_dir ./plots/multi_delay/$rag_name-e2e/original $args

    python delay.py --tag $rag_name-e2e \
        --instance_dirs $instance_dirs_str \
        --instance_groups $instance_groups_str \
        --include_sources rag_client \
        --include_route \
        --merge_task \
        --save_dir ./plots/multi_delay/$rag_name-e2e/merged $args
}

# Naive RAG Delay Analysis Script
run_naive_rag(){
    dataset=$1
    nreqs=$2
    rag_type="naive"
    rag_name=$rag_type"_rag"
    instance_dirs=(
        /mnt/data/agbench/results/rag-v0/$rag_name/stage-2/$rag_type-$dataset-OneByOne-0.0-$nreqs/results/client/monitor/rag_client
        /mnt/data/agbench/results/rag-v0/$rag_name/stage-2/$rag_type-$dataset-OneByOne-0.0-$nreqs/results/server/monitor/rag_server
        /mnt/data/agbench/results/rag-v0/$rag_name/stage-4/$rag_type-$dataset-FixedInterval-1.0-$nreqs/results/client/monitor/rag_client
        /mnt/data/agbench/results/rag-v0/$rag_name/stage-4/$rag_type-$dataset-FixedInterval-1.0-$nreqs/results/server/monitor/rag_server
        /mnt/data/agbench/results/rag-v0/$rag_name/stage-4/$rag_type-$dataset-FixedInterval-2.0-$nreqs/results/client/monitor/rag_client
        /mnt/data/agbench/results/rag-v0/$rag_name/stage-4/$rag_type-$dataset-FixedInterval-2.0-$nreqs/results/server/monitor/rag_server
        /mnt/data/agbench/results/rag-v0/$rag_name/stage-4/$rag_type-$dataset-FixedInterval-4.0-$nreqs/results/client/monitor/rag_client
        /mnt/data/agbench/results/rag-v0/$rag_name/stage-4/$rag_type-$dataset-FixedInterval-4.0-$nreqs/results/server/monitor/rag_server
        /mnt/data/agbench/results/rag-v0/$rag_name/stage-4/$rag_type-$dataset-FixedInterval-8.0-$nreqs/results/client/monitor/rag_client
        /mnt/data/agbench/results/rag-v0/$rag_name/stage-4/$rag_type-$dataset-FixedInterval-8.0-$nreqs/results/server/monitor/rag_server
    )
    instance_groups=(
        "bsl"
        "bsl"
        "nthreads=32;rps=1"
        "nthreads=32;rps=1"
        "nthreads=32;rps=2"
        "nthreads=32;rps=2"
        "nthreads=32;rps=4"
        "nthreads=32;rps=4"
        "nthreads=32;rps=8"
        "nthreads=32;rps=8"
    )
    # concatenate instance directories into a single string
    instance_dirs_str=$(printf " %s" "${instance_dirs[@]}")
    # concatenate instance groups into a single string
    instance_groups_str=$(printf " %s" "${instance_groups[@]}")

    run_rag "$instance_dirs_str" "$instance_groups_str" $dataset"_"$rag_name
}

# Advanced RAG Delay Analysis Script
run_advanced_rag(){
    dataset=$1
    nreqs=$2
    rag_type="advanced"
    rag_name=$rag_type"_rag"
    instance_dirs=(
        /mnt/data/agbench/results/rag-v0/$rag_name/stage-2/$rag_type-$dataset-OneByOne-0.0-$nreqs/results/client/monitor/rag_client
        /mnt/data/agbench/results/rag-v0/$rag_name/stage-2/$rag_type-$dataset-OneByOne-0.0-$nreqs/results/server/monitor/rag_server
        /mnt/data/agbench/results/rag-v0/$rag_name/stage-4/$rag_type-$dataset-FixedInterval-1.0-$nreqs/results/client/monitor/rag_client
        /mnt/data/agbench/results/rag-v0/$rag_name/stage-4/$rag_type-$dataset-FixedInterval-1.0-$nreqs/results/server/monitor/rag_server
        /mnt/data/agbench/results/rag-v0/$rag_name/stage-4/$rag_type-$dataset-FixedInterval-2.0-$nreqs/results/client/monitor/rag_client
        /mnt/data/agbench/results/rag-v0/$rag_name/stage-4/$rag_type-$dataset-FixedInterval-2.0-$nreqs/results/server/monitor/rag_server
        /mnt/data/agbench/results/rag-v0/$rag_name/stage-4/$rag_type-$dataset-FixedInterval-4.0-$nreqs/results/client/monitor/rag_client
        /mnt/data/agbench/results/rag-v0/$rag_name/stage-4/$rag_type-$dataset-FixedInterval-4.0-$nreqs/results/server/monitor/rag_server
        /mnt/data/agbench/results/rag-v0/$rag_name/stage-4/$rag_type-$dataset-FixedInterval-8.0-$nreqs/results/client/monitor/rag_client
        /mnt/data/agbench/results/rag-v0/$rag_name/stage-4/$rag_type-$dataset-FixedInterval-8.0-$nreqs/results/server/monitor/rag_server
    )
    instance_groups=(
        "bsl"
        "bsl"
        "nthreads=32;rps=1"
        "nthreads=32;rps=1"
        "nthreads=32;rps=2"
        "nthreads=32;rps=2"
        "nthreads=32;rps=4"
        "nthreads=32;rps=4"
        "nthreads=32;rps=8"
        "nthreads=32;rps=8"
    )
    # concatenate instance directories into a single string
    instance_dirs_str=$(printf " %s" "${instance_dirs[@]}")
    # concatenate instance groups into a single string
    instance_groups_str=$(printf " %s" "${instance_groups[@]}")

    run_rag "$instance_dirs_str" "$instance_groups_str" $dataset"_"$rag_name
}

# Dynamic RAG Delay Analysis Script
run_dynamic_rag(){
    dataset=$1
    nreqs=$2
    rag_type="dynamic"
    rag_name=$rag_type"_rag"
    instance_dirs=(
        /mnt/data/agbench/results/rag-v0/$rag_name/stage-2/$rag_type-$dataset-OneByOne-0.0-$nreqs/results/client/monitor/rag_client
        /mnt/data/agbench/results/rag-v0/$rag_name/stage-2/$rag_type-$dataset-OneByOne-0.0-$nreqs/results/server/monitor/rag_server
        /mnt/data/agbench/results/rag-v0/$rag_name/stage-4/$rag_type-$dataset-FixedInterval-1.0-$nreqs/results/client/monitor/rag_client
        /mnt/data/agbench/results/rag-v0/$rag_name/stage-4/$rag_type-$dataset-FixedInterval-1.0-$nreqs/results/server/monitor/rag_server
        /mnt/data/agbench/results/rag-v0/$rag_name/stage-4/$rag_type-$dataset-FixedInterval-2.0-$nreqs/results/client/monitor/rag_client
        /mnt/data/agbench/results/rag-v0/$rag_name/stage-4/$rag_type-$dataset-FixedInterval-2.0-$nreqs/results/server/monitor/rag_server
        /mnt/data/agbench/results/rag-v0/$rag_name/stage-4/$rag_type-$dataset-FixedInterval-4.0-$nreqs/results/client/monitor/rag_client
        /mnt/data/agbench/results/rag-v0/$rag_name/stage-4/$rag_type-$dataset-FixedInterval-4.0-$nreqs/results/server/monitor/rag_server
        /mnt/data/agbench/results/rag-v0/$rag_name/stage-4/$rag_type-$dataset-FixedInterval-8.0-$nreqs/results/client/monitor/rag_client
        /mnt/data/agbench/results/rag-v0/$rag_name/stage-4/$rag_type-$dataset-FixedInterval-8.0-$nreqs/results/server/monitor/rag_server
    )
    instance_groups=(
        "bsl"
        "bsl"
        "nthreads=32;rps=1"
        "nthreads=32;rps=1"
        "nthreads=32;rps=2"
        "nthreads=32;rps=2"
        "nthreads=32;rps=4"
        "nthreads=32;rps=4"
        "nthreads=32;rps=8"
        "nthreads=32;rps=8"
    )
    # concatenate instance directories into a single string
    instance_dirs_str=$(printf " %s" "${instance_dirs[@]}")
    # concatenate instance groups into a single string
    instance_groups_str=$(printf " %s" "${instance_groups[@]}")

    run_rag "$instance_dirs_str" "$instance_groups_str" $dataset"_"$rag_name
}

# run_hgpt
# run_drs
# run_swe
# run_webarena
# run_moa
# run_naive_rag WQA 10000
# run_advanced_rag WQA 10000
# run_dynamic_rag WQA 10000
# run_naive_rag NQ 10000
# run_advanced_rag NQ 10000
# run_dynamic_rag NQ 10000
# run_naive_rag MSMARCO 3000
# run_advanced_rag MSMARCO 3000
# run_dynamic_rag MSMARCO 3000
