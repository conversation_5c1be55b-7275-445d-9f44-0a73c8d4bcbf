#!/bin/bash

args="$@"
if [ -z "$args" ]; then
    echo "No additional arguments provided. $args"
else
    echo "Running delay analysis with arguments: $args"
fi

# Hugging GPT Delay Analysis Script
python delay.py --tag hgpt \
    --instance_dirs /mnt/data/agbench/results/hugginggpt/monitor/obo/hugginggpt_client \
    /mnt/data/agbench/results/hugginggpt/monitor/obo/hugginggpt_server \
    --exclude_tasks hugginggpt_e2e \
    --include_route \
    --include_delay_types e2e \
    --save_dir ./plots/delay/hgpt/original $args

python delay.py --tag hgpt \
    --instance_dirs /mnt/data/agbench/results/hugginggpt/monitor/obo/hugginggpt_client \
    /mnt/data/agbench/results/hugginggpt/monitor/obo/hugginggpt_server \
    --exclude_tasks hugginggpt_e2e \
    --include_route \
    --include_delay_types e2e \
    --merge_task \
    --save_dir ./plots/delay/hgpt/merged $args

python delay.py --tag hgpt-e2e \
    --instance_dirs /mnt/data/agbench/results/hugginggpt/monitor/obo/hugginggpt_client \
    /mnt/data/agbench/results/hugginggpt/monitor/obo/hugginggpt_server \
    --include_sources hugginggpt_client \
    --include_route \
    --save_dir ./plots/delay/hgpt-e2e/original $args

python delay.py --tag hgpt-e2e \
    --instance_dirs /mnt/data/agbench/results/hugginggpt/monitor/obo/hugginggpt_client \
    /mnt/data/agbench/results/hugginggpt/monitor/obo/hugginggpt_server \
    --include_sources hugginggpt_client \
    --include_route \
    --merge_task \
    --save_dir ./plots/delay/hgpt-e2e/merged $args

# Deep Research Delay Analysis Script
python delay.py --tag drs \
    --instance_dirs /mnt/data/agbench/results/drs/final/baseline/stage-2/drs_async-YDCDRS-OneByOne-0.0-202/results/client/monitor/client \
    /mnt/data/agbench/results/drs/final/baseline/stage-2/drs_async-YDCDRS-OneByOne-0.0-202/results/server/monitor/server \
    --include_tasks with_llm_async search_with_queries_async split_texts_async embed_docs_async store_vdb_async embed_texts_async search_vdb_async clear_vdb_async \
    --exclude_tasks inner_embed_texts_async \
    --exclude_tasks_in_tree summarize \
    --include_route \
    --include_delay_types e2e \
    --save_dir ./plots/delay/drs/original $args

python delay.py --tag drs \
    --instance_dirs /mnt/data/agbench/results/drs/final/baseline/stage-2/drs_async-YDCDRS-OneByOne-0.0-202/results/client/monitor/client \
    /mnt/data/agbench/results/drs/final/baseline/stage-2/drs_async-YDCDRS-OneByOne-0.0-202/results/server/monitor/server \
    --include_tasks with_llm_async search_with_queries_async split_texts_async embed_docs_async store_vdb_async embed_texts_async search_vdb_async clear_vdb_async \
    --exclude_tasks inner_embed_texts_async \
    --exclude_tasks_in_tree summarize \
    --include_route \
    --include_delay_types e2e \
    --merge_task \
    --save_dir ./plots/delay/drs/merged $args

python delay.py --tag drs-e2e \
    --instance_dirs /mnt/data/agbench/results/drs/final/baseline/stage-2/drs_async-YDCDRS-OneByOne-0.0-202/results/client/monitor/client \
    /mnt/data/agbench/results/drs/final/baseline/stage-2/drs_async-YDCDRS-OneByOne-0.0-202/results/server/monitor/server \
    --include_sources client \
    --include_tasks workflow_async \
    --exclude_tasks_in_tree summarize \
    --include_route \
    --save_dir ./plots/delay/drs-e2e/original $args

python delay.py --tag drs-e2e \
    --instance_dirs /mnt/data/agbench/results/drs/final/baseline/stage-2/drs_async-YDCDRS-OneByOne-0.0-202/results/client/monitor/client \
    /mnt/data/agbench/results/drs/final/baseline/stage-2/drs_async-YDCDRS-OneByOne-0.0-202/results/server/monitor/server \
    --include_sources client \
    --include_tasks workflow_async \
    --exclude_tasks_in_tree summarize \
    --include_route \
    --merge_task \
    --save_dir ./plots/delay/drs-e2e/merged $args

# SWE Agent Delay Analysis Script
python delay.py --tag swe \
    --instance_dirs /mnt/data/agbench/results/swe/swe-ds32B/stage-2/data/swe-agent/client \
    /mnt/data/agbench/results/swe/swe-ds32B/stage-2/data/swe-agent/model \
    /mnt/data/agbench/results/swe/swe-ds32B/stage-2/data/swe-agent/server \
    /mnt/data/agbench/results/swe/swe-ds32B/stage-2/data/swe-agent/SWEEnv \
    --exclude_tasks swe_agent_request \
    --include_route \
    --include_delay_types e2e \
    --save_dir ./plots/delay/swe/original $args

python delay.py --tag swe \
    --instance_dirs /mnt/data/agbench/results/swe/swe-ds32B/stage-2/data/swe-agent/client \
    /mnt/data/agbench/results/swe/swe-ds32B/stage-2/data/swe-agent/model \
    /mnt/data/agbench/results/swe/swe-ds32B/stage-2/data/swe-agent/server \
    /mnt/data/agbench/results/swe/swe-ds32B/stage-2/data/swe-agent/SWEEnv \
    --exclude_tasks swe_agent_request \
    --include_route \
    --include_delay_types e2e \
    --merge_task \
    --save_dir ./plots/delay/swe/merged $args

python delay.py --tag swe-e2e \
    --instance_dirs /mnt/data/agbench/results/swe/swe-ds32B/stage-2/data/swe-agent/client \
    /mnt/data/agbench/results/swe/swe-ds32B/stage-2/data/swe-agent/model \
    /mnt/data/agbench/results/swe/swe-ds32B/stage-2/data/swe-agent/server \
    /mnt/data/agbench/results/swe/swe-ds32B/stage-2/data/swe-agent/SWEEnv \
    --include_sources client \
    --include_route \
    --save_dir ./plots/delay/swe-e2e/original $args

python delay.py --tag swe-e2e \
    --instance_dirs /mnt/data/agbench/results/swe/swe-ds32B/stage-2/data/swe-agent/client \
    /mnt/data/agbench/results/swe/swe-ds32B/stage-2/data/swe-agent/model \
    /mnt/data/agbench/results/swe/swe-ds32B/stage-2/data/swe-agent/server \
    /mnt/data/agbench/results/swe/swe-ds32B/stage-2/data/swe-agent/SWEEnv \
    --include_sources client \
    --include_route \
    --merge_task \
    --save_dir ./plots/delay/swe-e2e/merged $args

# WebArena Delay Analysis Script
python delay.py --tag webarena \
    --instance_dirs /mnt/data/agbench/results/webarena/monitor/obo/client \
    /mnt/data/agbench/results/webarena/monitor/obo/env_manager \
    /mnt/data/agbench/results/webarena/monitor/obo/execution_coordinator \
    /mnt/data/agbench/results/webarena/monitor/obo/llm \
    --exclude_tasks e2e_execution \
    --include_route \
    --include_delay_types e2e \
    --save_dir ./plots/delay/webarena/original $args

python delay.py --tag webarena \
    --instance_dirs /mnt/data/agbench/results/webarena/monitor/obo/client \
    /mnt/data/agbench/results/webarena/monitor/obo/env_manager \
    /mnt/data/agbench/results/webarena/monitor/obo/execution_coordinator \
    /mnt/data/agbench/results/webarena/monitor/obo/llm \
    --exclude_tasks e2e_execution \
    --include_route \
    --include_delay_types e2e \
    --merge_task \
    --save_dir ./plots/delay/webarena/merged $args

python delay.py --tag webarena-e2e \
    --instance_dirs /mnt/data/agbench/results/webarena/monitor/obo/client \
    /mnt/data/agbench/results/webarena/monitor/obo/env_manager \
    /mnt/data/agbench/results/webarena/monitor/obo/execution_coordinator \
    /mnt/data/agbench/results/webarena/monitor/obo/llm \
    --include_sources client \
    --include_route \
    --save_dir ./plots/delay/webarena-e2e/original $args

python delay.py --tag webarena-e2e \
    --instance_dirs /mnt/data/agbench/results/webarena/monitor/obo/client \
    /mnt/data/agbench/results/webarena/monitor/obo/env_manager \
    /mnt/data/agbench/results/webarena/monitor/obo/execution_coordinator \
    /mnt/data/agbench/results/webarena/monitor/obo/llm \
    --include_sources client \
    --include_route \
    --merge_task \
    --save_dir ./plots/delay/webarena-e2e/merged $args

# MoA Delay Analysis Script
python delay.py --tag moa \
    --instance_dirs /mnt/data/agbench/results/moa/monitor/obo/moa_client \
    /mnt/data/agbench/results/moa/monitor/obo/moa_service \
    --exclude_tasks moa_chat layer_inference \
    --include_route \
    --include_delay_types e2e \
    --save_dir ./plots/delay/moa/original $args

python delay.py --tag moa \
    --instance_dirs /mnt/data/agbench/results/moa/monitor/obo/moa_client \
    /mnt/data/agbench/results/moa/monitor/obo/moa_service \
    --exclude_tasks moa_chat layer_inference \
    --include_route \
    --include_delay_types e2e \
    --merge_task \
    --save_dir ./plots/delay/moa/merged $args

python delay.py --tag moa-e2e \
    --instance_dirs /mnt/data/agbench/results/moa/monitor/obo/moa_client \
    /mnt/data/agbench/results/moa/monitor/obo/moa_service \
    --include_sources moa_client \
    --include_route \
    --save_dir ./plots/delay/moa-e2e/original $args

python delay.py --tag moa-e2e \
    --instance_dirs /mnt/data/agbench/results/moa/monitor/obo/moa_client \
    /mnt/data/agbench/results/moa/monitor/obo/moa_service \
    --include_sources moa_client \
    --include_route \
    --merge_task \
    --save_dir ./plots/delay/moa-e2e/merged $args

## RAG + WQA Dataset
# Naive RAG Delay Analysis Script
python delay.py --tag wqa_naive_rag \
    --instance_dirs /mnt/data/agbench/results/rag-v0/naive_rag/stage-2/naive-WQA-OneByOne-0.0-10000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/naive_rag/stage-2/naive-WQA-OneByOne-0.0-10000/results/server/monitor/rag_server \
    --include_route \
    --include_delay_types e2e \
    --include_tasks retrieval_judger query_translation direct_generation normal_generation load_docs split_docs embed_documents vdb_store embed_query vdb_search reranking \
    --save_dir ./plots/delay/wqa_naive_rag/original $args

python delay.py --tag wqa_naive_rag \
    --instance_dirs /mnt/data/agbench/results/rag-v0/naive_rag/stage-2/naive-WQA-OneByOne-0.0-10000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/naive_rag/stage-2/naive-WQA-OneByOne-0.0-10000/results/server/monitor/rag_server \
    --include_route \
    --include_delay_types e2e \
    --include_tasks retrieval_judger query_translation direct_generation normal_generation load_docs split_docs embed_documents vdb_store embed_query vdb_search reranking \
    --merge_task \
    --save_dir ./plots/delay/wqa_naive_rag/merged $args

python delay.py --tag wqa_naive_rag-e2e \
    --instance_dirs /mnt/data/agbench/results/rag-v0/naive_rag/stage-2/naive-WQA-OneByOne-0.0-10000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/naive_rag/stage-2/naive-WQA-OneByOne-0.0-10000/results/server/monitor/rag_server \
    --include_sources rag_client \
    --include_route \
    --save_dir ./plots/delay/wqa_naive_rag-e2e/original $args

python delay.py --tag wqa_naive_rag-e2e \
    --instance_dirs /mnt/data/agbench/results/rag-v0/naive_rag/stage-2/naive-WQA-OneByOne-0.0-10000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/naive_rag/stage-2/naive-WQA-OneByOne-0.0-10000/results/server/monitor/rag_server \
    --include_sources rag_client \
    --include_route \
    --merge_task \
    --save_dir ./plots/delay/wqa_naive_rag-e2e/merged $args

# Advanced RAG Delay Analysis Script
python delay.py --tag wqa_advanced_rag \
    --instance_dirs /mnt/data/agbench/results/rag-v0/advanced_rag/stage-2/advanced-WQA-OneByOne-0.0-10000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/advanced_rag/stage-2/advanced-WQA-OneByOne-0.0-10000/results/server/monitor/rag_server \
    --include_route \
    --include_delay_types e2e \
    --include_tasks retrieval_judger query_translation direct_generation normal_generation load_docs split_docs embed_documents vdb_store embed_query vdb_search reranking \
    --save_dir ./plots/delay/wqa_advanced_rag/original $args

python delay.py --tag wqa_advanced_rag \
    --instance_dirs /mnt/data/agbench/results/rag-v0/advanced_rag/stage-2/advanced-WQA-OneByOne-0.0-10000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/advanced_rag/stage-2/advanced-WQA-OneByOne-0.0-10000/results/server/monitor/rag_server \
    --include_route \
    --include_delay_types e2e \
    --include_tasks retrieval_judger query_translation direct_generation normal_generation load_docs split_docs embed_documents vdb_store embed_query vdb_search reranking \
    --merge_task \
    --save_dir ./plots/delay/wqa_advanced_rag/merged $args

python delay.py --tag wqa_advanced_rag-e2e \
    --instance_dirs /mnt/data/agbench/results/rag-v0/advanced_rag/stage-2/advanced-WQA-OneByOne-0.0-10000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/advanced_rag/stage-2/advanced-WQA-OneByOne-0.0-10000/results/server/monitor/rag_server \
    --include_sources rag_client \
    --include_route \
    --save_dir ./plots/delay/wqa_advanced_rag-e2e/original $args

python delay.py --tag wqa_advanced_rag-e2e \
    --instance_dirs /mnt/data/agbench/results/rag-v0/advanced_rag/stage-2/advanced-WQA-OneByOne-0.0-10000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/advanced_rag/stage-2/advanced-WQA-OneByOne-0.0-10000/results/server/monitor/rag_server \
    --include_sources rag_client \
    --include_route \
    --merge_task \
    --save_dir ./plots/delay/wqa_advanced_rag-e2e/merged $args

# Dynamic RAG Delay Analysis Script
python delay.py --tag wqa_dynamic_rag \
    --instance_dirs /mnt/data/agbench/results/rag-v0/dynamic_rag/stage-2/dynamic-WQA-OneByOne-0.0-10000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/dynamic_rag/stage-2/dynamic-WQA-OneByOne-0.0-10000/results/server/monitor/rag_server \
    --include_route \
    --include_delay_types e2e \
    --include_tasks retrieval_judger query_translation direct_generation normal_generation load_docs split_docs embed_documents vdb_store embed_query vdb_search reranking \
    --save_dir ./plots/delay/wqa_dynamic_rag/original $args

python delay.py --tag wqa_dynamic_rag \
    --instance_dirs /mnt/data/agbench/results/rag-v0/dynamic_rag/stage-2/dynamic-WQA-OneByOne-0.0-10000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/dynamic_rag/stage-2/dynamic-WQA-OneByOne-0.0-10000/results/server/monitor/rag_server \
    --include_route \
    --include_delay_types e2e \
    --include_tasks retrieval_judger query_translation direct_generation normal_generation load_docs split_docs embed_documents vdb_store embed_query vdb_search reranking \
    --merge_task \
    --save_dir ./plots/delay/wqa_dynamic_rag/merged $args

python delay.py --tag wqa_dynamic_rag-e2e \
    --instance_dirs /mnt/data/agbench/results/rag-v0/dynamic_rag/stage-2/dynamic-WQA-OneByOne-0.0-10000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/dynamic_rag/stage-2/dynamic-WQA-OneByOne-0.0-10000/results/server/monitor/rag_server \
    --include_sources rag_client \
    --include_route \
    --save_dir ./plots/delay/wqa_dynamic_rag-e2e/original $args

python delay.py --tag wqa_dynamic_rag-e2e \
    --instance_dirs /mnt/data/agbench/results/rag-v0/dynamic_rag/stage-2/dynamic-WQA-OneByOne-0.0-10000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/dynamic_rag/stage-2/dynamic-WQA-OneByOne-0.0-10000/results/server/monitor/rag_server \
    --include_sources rag_client \
    --include_route \
    --merge_task \
    --save_dir ./plots/delay/wqa_dynamic_rag-e2e/merged $args

## RAG + NQ Dataset
# Naive RAG Delay Analysis Script
python delay.py --tag nq_naive_rag \
    --instance_dirs /mnt/data/agbench/results/rag-v0/naive_rag/stage-2/naive-NQ-OneByOne-0.0-10000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/naive_rag/stage-2/naive-NQ-OneByOne-0.0-10000/results/server/monitor/rag_server \
    --include_route \
    --include_delay_types e2e \
    --include_tasks retrieval_judger query_translation direct_generation normal_generation load_docs split_docs embed_documents vdb_store embed_query vdb_search reranking \
    --save_dir ./plots/delay/nq_naive_rag/original $args

python delay.py --tag nq_naive_rag \
    --instance_dirs /mnt/data/agbench/results/rag-v0/naive_rag/stage-2/naive-NQ-OneByOne-0.0-10000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/naive_rag/stage-2/naive-NQ-OneByOne-0.0-10000/results/server/monitor/rag_server \
    --include_route \
    --include_delay_types e2e \
    --include_tasks retrieval_judger query_translation direct_generation normal_generation load_docs split_docs embed_documents vdb_store embed_query vdb_search reranking \
    --merge_task \
    --save_dir ./plots/delay/nq_naive_rag/merged $args

python delay.py --tag nq_naive_rag-e2e \
    --instance_dirs /mnt/data/agbench/results/rag-v0/naive_rag/stage-2/naive-NQ-OneByOne-0.0-10000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/naive_rag/stage-2/naive-NQ-OneByOne-0.0-10000/results/server/monitor/rag_server \
    --include_sources rag_client \
    --include_route \
    --save_dir ./plots/delay/nq_naive_rag-e2e/original $args

python delay.py --tag nq_naive_rag-e2e \
    --instance_dirs /mnt/data/agbench/results/rag-v0/naive_rag/stage-2/naive-NQ-OneByOne-0.0-10000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/naive_rag/stage-2/naive-NQ-OneByOne-0.0-10000/results/server/monitor/rag_server \
    --include_sources rag_client \
    --include_route \
    --merge_task \
    --save_dir ./plots/delay/nq_naive_rag-e2e/merged $args

# Advanced RAG Delay Analysis Script
python delay.py --tag nq_advanced_rag \
    --instance_dirs /mnt/data/agbench/results/rag-v0/advanced_rag/stage-2/advanced-NQ-OneByOne-0.0-10000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/advanced_rag/stage-2/advanced-NQ-OneByOne-0.0-10000/results/server/monitor/rag_server \
    --include_route \
    --include_delay_types e2e \
    --include_tasks retrieval_judger query_translation direct_generation normal_generation load_docs split_docs embed_documents vdb_store embed_query vdb_search reranking \
    --save_dir ./plots/delay/nq_advanced_rag/original $args

python delay.py --tag nq_advanced_rag \
    --instance_dirs /mnt/data/agbench/results/rag-v0/advanced_rag/stage-2/advanced-NQ-OneByOne-0.0-10000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/advanced_rag/stage-2/advanced-NQ-OneByOne-0.0-10000/results/server/monitor/rag_server \
    --include_route \
    --include_delay_types e2e \
    --include_tasks retrieval_judger query_translation direct_generation normal_generation load_docs split_docs embed_documents vdb_store embed_query vdb_search reranking \
    --merge_task \
    --save_dir ./plots/delay/nq_advanced_rag/merged $args

python delay.py --tag nq_advanced_rag-e2e \
    --instance_dirs /mnt/data/agbench/results/rag-v0/advanced_rag/stage-2/advanced-NQ-OneByOne-0.0-10000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/advanced_rag/stage-2/advanced-NQ-OneByOne-0.0-10000/results/server/monitor/rag_server \
    --include_sources rag_client \
    --include_route \
    --save_dir ./plots/delay/nq_advanced_rag-e2e/original $args

python delay.py --tag nq_advanced_rag-e2e \
    --instance_dirs /mnt/data/agbench/results/rag-v0/advanced_rag/stage-2/advanced-NQ-OneByOne-0.0-10000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/advanced_rag/stage-2/advanced-NQ-OneByOne-0.0-10000/results/server/monitor/rag_server \
    --include_sources rag_client \
    --include_route \
    --merge_task \
    --save_dir ./plots/delay/nq_advanced_rag-e2e/merged $args

# Dynamic RAG Delay Analysis Script
python delay.py --tag nq_dynamic_rag \
    --instance_dirs /mnt/data/agbench/results/rag-v0/dynamic_rag/stage-2/dynamic-NQ-OneByOne-0.0-10000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/dynamic_rag/stage-2/dynamic-NQ-OneByOne-0.0-10000/results/server/monitor/rag_server \
    --include_route \
    --include_delay_types e2e \
    --include_tasks retrieval_judger query_translation direct_generation normal_generation load_docs split_docs embed_documents vdb_store embed_query vdb_search reranking \
    --save_dir ./plots/delay/nq_dynamic_rag/original $args

python delay.py --tag nq_dynamic_rag \
    --instance_dirs /mnt/data/agbench/results/rag-v0/dynamic_rag/stage-2/dynamic-NQ-OneByOne-0.0-10000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/dynamic_rag/stage-2/dynamic-NQ-OneByOne-0.0-10000/results/server/monitor/rag_server \
    --include_route \
    --include_delay_types e2e \
    --include_tasks retrieval_judger query_translation direct_generation normal_generation load_docs split_docs embed_documents vdb_store embed_query vdb_search reranking \
    --merge_task \
    --save_dir ./plots/delay/nq_dynamic_rag/merged $args

python delay.py --tag nq_dynamic_rag-e2e \
    --instance_dirs /mnt/data/agbench/results/rag-v0/dynamic_rag/stage-2/dynamic-NQ-OneByOne-0.0-10000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/dynamic_rag/stage-2/dynamic-NQ-OneByOne-0.0-10000/results/server/monitor/rag_server \
    --include_sources rag_client \
    --include_route \
    --save_dir ./plots/delay/nq_dynamic_rag-e2e/original $args

python delay.py --tag nq_dynamic_rag-e2e \
    --instance_dirs /mnt/data/agbench/results/rag-v0/dynamic_rag/stage-2/dynamic-NQ-OneByOne-0.0-10000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/dynamic_rag/stage-2/dynamic-NQ-OneByOne-0.0-10000/results/server/monitor/rag_server \
    --include_sources rag_client \
    --include_route \
    --merge_task \
    --save_dir ./plots/delay/nq_dynamic_rag-e2e/merged $args

## RAG + MSMARCO Dataset
# Naive RAG Delay Analysis Script
python delay.py --tag msmarco_naive_rag \
    --instance_dirs /mnt/data/agbench/results/rag-v0/naive_rag/stage-2/naive-MSMARCO-OneByOne-0.0-3000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/naive_rag/stage-2/naive-MSMARCO-OneByOne-0.0-3000/results/server/monitor/rag_server \
    --include_route \
    --include_delay_types e2e \
    --include_tasks retrieval_judger query_translation direct_generation normal_generation load_docs split_docs embed_documents vdb_store embed_query vdb_search reranking \
    --save_dir ./plots/delay/msmarco_naive_rag/original $args

python delay.py --tag msmarco_naive_rag \
    --instance_dirs /mnt/data/agbench/results/rag-v0/naive_rag/stage-2/naive-MSMARCO-OneByOne-0.0-3000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/naive_rag/stage-2/naive-MSMARCO-OneByOne-0.0-3000/results/server/monitor/rag_server \
    --include_route \
    --include_delay_types e2e \
    --include_tasks retrieval_judger query_translation direct_generation normal_generation load_docs split_docs embed_documents vdb_store embed_query vdb_search reranking \
    --merge_task \
    --save_dir ./plots/delay/msmarco_naive_rag/merged $args

python delay.py --tag msmarco_naive_rag-e2e \
    --instance_dirs /mnt/data/agbench/results/rag-v0/naive_rag/stage-2/naive-MSMARCO-OneByOne-0.0-3000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/naive_rag/stage-2/naive-MSMARCO-OneByOne-0.0-3000/results/server/monitor/rag_server \
    --include_sources rag_client \
    --include_route \
    --save_dir ./plots/delay/msmarco_naive_rag-e2e/original $args

python delay.py --tag msmarco_naive_rag-e2e \
    --instance_dirs /mnt/data/agbench/results/rag-v0/naive_rag/stage-2/naive-MSMARCO-OneByOne-0.0-3000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/naive_rag/stage-2/naive-MSMARCO-OneByOne-0.0-3000/results/server/monitor/rag_server \
    --include_sources rag_client \
    --include_route \
    --merge_task \
    --save_dir ./plots/delay/msmarco_naive_rag-e2e/merged $args

# Advanced RAG Delay Analysis Script
python delay.py --tag msmarco_advanced_rag \
    --instance_dirs /mnt/data/agbench/results/rag-v0/advanced_rag/stage-2/advanced-MSMARCO-OneByOne-0.0-3000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/advanced_rag/stage-2/advanced-MSMARCO-OneByOne-0.0-3000/results/server/monitor/rag_server \
    --include_route \
    --include_delay_types e2e \
    --include_tasks retrieval_judger query_translation direct_generation normal_generation load_docs split_docs embed_documents vdb_store embed_query vdb_search reranking \
    --save_dir ./plots/delay/msmarco_advanced_rag/original $args

python delay.py --tag msmarco_advanced_rag \
    --instance_dirs /mnt/data/agbench/results/rag-v0/advanced_rag/stage-2/advanced-MSMARCO-OneByOne-0.0-3000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/advanced_rag/stage-2/advanced-MSMARCO-OneByOne-0.0-3000/results/server/monitor/rag_server \
    --include_route \
    --include_delay_types e2e \
    --include_tasks retrieval_judger query_translation direct_generation normal_generation load_docs split_docs embed_documents vdb_store embed_query vdb_search reranking \
    --merge_task \
    --save_dir ./plots/delay/msmarco_advanced_rag/merged $args

python delay.py --tag msmarco_advanced_rag-e2e \
    --instance_dirs /mnt/data/agbench/results/rag-v0/advanced_rag/stage-2/advanced-MSMARCO-OneByOne-0.0-3000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/advanced_rag/stage-2/advanced-MSMARCO-OneByOne-0.0-3000/results/server/monitor/rag_server \
    --include_sources rag_client \
    --include_route \
    --save_dir ./plots/delay/msmarco_advanced_rag-e2e/original $args

python delay.py --tag msmarco_advanced_rag-e2e \
    --instance_dirs /mnt/data/agbench/results/rag-v0/advanced_rag/stage-2/advanced-MSMARCO-OneByOne-0.0-3000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/advanced_rag/stage-2/advanced-MSMARCO-OneByOne-0.0-3000/results/server/monitor/rag_server \
    --include_sources rag_client \
    --include_route \
    --merge_task \
    --save_dir ./plots/delay/msmarco_advanced_rag-e2e/merged $args

# Dynamic RAG Delay Analysis Script
python delay.py --tag msmarco_dynamic_rag \
    --instance_dirs /mnt/data/agbench/results/rag-v0/dynamic_rag/stage-2/dynamic-MSMARCO-OneByOne-0.0-3000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/dynamic_rag/stage-2/dynamic-MSMARCO-OneByOne-0.0-3000/results/server/monitor/rag_server \
    --include_route \
    --include_delay_types e2e \
    --include_tasks retrieval_judger query_translation direct_generation normal_generation load_docs split_docs embed_documents vdb_store embed_query vdb_search reranking \
    --save_dir ./plots/delay/msmarco_dynamic_rag/original $args

python delay.py --tag msmarco_dynamic_rag \
    --instance_dirs /mnt/data/agbench/results/rag-v0/dynamic_rag/stage-2/dynamic-MSMARCO-OneByOne-0.0-3000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/dynamic_rag/stage-2/dynamic-MSMARCO-OneByOne-0.0-3000/results/server/monitor/rag_server \
    --include_route \
    --include_delay_types e2e \
    --include_tasks retrieval_judger query_translation direct_generation normal_generation load_docs split_docs embed_documents vdb_store embed_query vdb_search reranking \
    --merge_task \
    --save_dir ./plots/delay/msmarco_dynamic_rag/merged $args

python delay.py --tag msmarco_dynamic_rag-e2e \
    --instance_dirs /mnt/data/agbench/results/rag-v0/dynamic_rag/stage-2/dynamic-MSMARCO-OneByOne-0.0-3000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/dynamic_rag/stage-2/dynamic-MSMARCO-OneByOne-0.0-3000/results/server/monitor/rag_server \
    --include_sources rag_client \
    --include_route \
    --save_dir ./plots/delay/msmarco_dynamic_rag-e2e/original $args

python delay.py --tag msmarco_dynamic_rag-e2e \
    --instance_dirs /mnt/data/agbench/results/rag-v0/dynamic_rag/stage-2/dynamic-MSMARCO-OneByOne-0.0-3000/results/client/monitor/rag_client \
    /mnt/data/agbench/results/rag-v0/dynamic_rag/stage-2/dynamic-MSMARCO-OneByOne-0.0-3000/results/server/monitor/rag_server \
    --include_sources rag_client \
    --include_route \
    --merge_task \
    --save_dir ./plots/delay/msmarco_dynamic_rag-e2e/merged $args
