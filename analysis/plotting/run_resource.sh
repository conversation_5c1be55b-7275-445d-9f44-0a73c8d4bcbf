python resource.py --collect-data \
    --prometheus-url http://localhost:9091 \
    --start-time "2025-07-24 08:11:26" \
    --end-time "2025-07-24 09:12:32" \
    --data-dir ./.cache/data/prometheus/swe-env \
    --tag 512threads \
    --include-containers "sweagent" "agbench" \
    --aggregate-containers "sweagent-swe-agent" \
    --max-containers 5 \
    --generate-report

python resource.py --collect-data \
    --prometheus-url http://localhost:9091 \
    --start-time "2025-07-24 09:13:09" \
    --end-time "2025-07-24 10:30:01" \
    --data-dir ./.cache/data/prometheus/swe-env \
    --tag 256threads \
    --include-containers "sweagent" "agbench" \
    --aggregate-containers "sweagent-swe-agent" \
    --max-containers 5 \
    --generate-report

python resource.py --collect-data \
    --prometheus-url http://localhost:9091 \
    --start-time "2025-07-24 10:30:37" \
    --end-time "2025-07-24 11:22:18" \
    --data-dir ./.cache/data/prometheus/swe-env \
    --tag 128threads \
    --include-containers "sweagent" "agbench" \
    --aggregate-containers "sweagent-swe-agent" \
    --max-containers 5 \
    --generate-report

python resource.py --collect-data \
    --prometheus-url http://localhost:9091 \
    --start-time "2025-07-24 11:22:54" \
    --end-time "2025-07-24 12:18:44" \
    --data-dir ./.cache/data/prometheus/swe-env \
    --tag 64threads \
    --include-containers "sweagent" "agbench" \
    --aggregate-containers "sweagent-swe-agent" \
    --max-containers 5 \
    --generate-report

python resource.py --collect-data \
    --prometheus-url http://localhost:9091 \
    --start-time "2025-07-24 12:19:20" \
    --end-time "2025-07-24 13:25:12" \
    --data-dir ./.cache/data/prometheus/swe-env \
    --tag 32threads \
    --include-containers "sweagent" "agbench" \
    --aggregate-containers "sweagent-swe-agent" \
    --max-containers 5 \
    --generate-report

python resource.py --collect-data \
    --prometheus-url http://localhost:9091 \
    --start-time "2025-07-24 13:25:48" \
    --end-time "2025-07-24 15:03:07" \
    --data-dir ./.cache/data/prometheus/swe-env \
    --tag 16threads \
    --include-containers "sweagent" "agbench" \
    --aggregate-containers "sweagent-swe-agent" \
    --max-containers 5 \
    --generate-report

python resource.py --collect-data \
    --prometheus-url http://localhost:9091 \
    --start-time "2025-07-24 15:03:43" \
    --end-time "2025-07-24 17:41:00" \
    --data-dir ./.cache/data/prometheus/swe-env \
    --tag 8threads \
    --include-containers "sweagent" "agbench" \
    --aggregate-containers "sweagent-swe-agent" \
    --max-containers 5 \
    --generate-report

python resource.py --collect-data \
    --prometheus-url http://localhost:9091 \
    --start-time "2025-07-24 17:41:35" \
    --end-time "2025-07-24 22:53:03" \
    --data-dir ./.cache/data/prometheus/swe-env \
    --tag 4threads \
    --include-containers "sweagent" "agbench" \
    --aggregate-containers "sweagent-swe-agent" \
    --max-containers 5 \
    --generate-report