import os
import pandas as pd
from pandas import DataFrame


def get_short_name_map(data: DataFrame) -> dict[str, str]:
    """Create a mapping of original task names to shortened names.
    The original task name follows the format part1.part2.part3...partN, where
    the right parts are usually more specific and more informative.
    For example, "task1.subtaskA.subtaskB" will be shortened to "subtaskB" or "subtaskA.subtaskB" if "subtaskB" only
    can not make sure all tasks are unique.
    We need to find the shortest names such that the shortened names are unique.
    Args:
        data: DataFrame containing task names.
    Returns:
        A dictionary mapping original task names to shortened names.
    """
    original_names = data["task_name"].unique()
    # sort by number of parts in the name (shorter names first)
    original_names = sorted(
        original_names, key=lambda x: len(x.split(".")), reverse=False
    )
    name_map = {}
    new_names = set()
    for name in original_names:
        parts = name.split(".")
        # Try to find the shortest unique name
        for i in range(len(parts)):
            short_name = ".".join(parts[-(i + 1) :])
            if short_name not in new_names:
                name_map[name] = short_name
                new_names.add(short_name)
                break

    print(f"Shortened task names: {name_map}")
    if len(original_names) > 1:
        # Now we can make it even shorter by removing the common prefix and suffix
        common_prefix = os.path.commonprefix(list(name_map.values()))
        common_suffix = os.path.commonprefix([name[::-1] for name in name_map.values()])
        if common_suffix:
            common_suffix = common_suffix[::-1]
        if common_suffix:
            # Remove the common suffix from all names
            name_map = {k: v[: -len(common_suffix)] for k, v in name_map.items()}
            print(f"Removed common suffix: {common_suffix}")
        else:
            print("No common suffix found.")
        if common_prefix:
            # Remove the common prefix from all names
            # name_map = {k: v[len(common_prefix) :] for k, v in name_map.items()}
            print(f"Removed common prefix: {common_prefix}")
        else:
            print("No common prefix found.")
    return name_map


def shorten_task_name(data: DataFrame) -> str:
    """Shorten task names for better readability in plots.
    The original task name follows the format part1.part2.part3...partN, where the right parts are usually more specific and more informative.
    For example, "task1.subtaskA.subtaskB" will be shortened to "subtaskB" or "subtaskA.subtaskB" of "subtaskB" only can not make sure all tasks are unique.
    We need to find the shortest names such that the shortened names are unique.
    Args:
        data: DataFrame containing task names.
    Returns:
        A DataFrame with shortened task names.
    """
    name_map = get_short_name_map(data)
    if not name_map:
        print("No task names to shorten.")
        return data
    # Apply the mapping to the DataFrame
    data["task_name"] = data["task_name"].map(name_map)
    return data
