import os, sys
import pandas as pd
from pandas import Data<PERSON>rame
from typing import Optional, Dict
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from tqdm import tqdm
import hashlib
import json
from difflib import SequenceMatcher
from typing import List, Tuple


module_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
print(f"Adding module path: {module_path}")
if module_path not in sys.path:
    print(f"Appending {module_path} to sys.path")
    sys.path.append(module_path)

from monitor.delay_data import ExecutionNode, ExecutionTree, parse_delay_data
from plotting.utils import shorten_task_name, get_short_name_map


def get_ordered_task_names(
    data: DataFrame,
    order_by: str = "start_time",
) -> List[str]:
    """
    Get ordered task names based on the specified order.

    Args:
        data: DataFrame containing delay data.
        order_by: Criteria to order tasks by ('start_time', 'end_time', 'mean', 'std').

    Returns:
        List of ordered task names.
    """
    if order_by == "start_time":
        ordered_task_names = data.sort_values(by="start_time")["task_name"].unique()
    elif order_by == "end_time":
        ordered_task_names = data.sort_values(by="end_time")["task_name"].unique()
    elif order_by == "mean":
        ordered_task_names = (
            data.groupby("task_name")["delay"]
            .agg(["mean", "std"])
            .reset_index()
            .sort_values(by="mean", ascending=False)["task_name"]
            .unique()
        )
    elif order_by == "std":
        ordered_task_names = (
            data.groupby("task_name")["delay"]
            .agg(["mean", "std"])
            .reset_index()
            .sort_values(by="std", ascending=False)["task_name"]
            .unique()
        )
    else:
        ordered_task_names = data["task_name"].unique()

    return ordered_task_names.tolist()


def get_fig(n_tasks: int):
    ncols = 2 if n_tasks > 2 else 1
    if n_tasks >= 6:
        ncols = 3
    if n_tasks >= 12:
        ncols = 4
    nrows = (n_tasks + ncols - 1) // ncols

    fig, axes = plt.subplots(
        nrows, ncols, figsize=(6 * ncols, 4 * nrows), squeeze=False
    )
    axes = axes.flatten()
    return fig, axes, nrows, ncols


def plot_task_delay_bars(
    data: DataFrame,
    tag: str = "test_agent",
    save_dir: Optional[str] = "./plots",
    **kwargs: Dict[str, str],
) -> str:
    """
    Plot the end-to-end delay distribution for each task in the data.

    Args:
        data: DataFrame containing delay data.
        tag: Tag for the plot title.
        save_dir: Directory to save the plot.
        **kwargs: Additional keyword arguments for customization.

    Returns:
        Path to the saved plot image.
    """
    print(f"Plotting task delay bars for tag: {tag}")
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    # Shorten task names for better readability
    data = shorten_task_name(data)

    # Group by task name and calculate mean and std deviation of delays
    grouped_data = data.groupby("task_name")["delay"].agg(["mean", "std"]).reset_index()

    # get ordered task names based on the first occurrence in the data
    order_by = kwargs.get("order_by", "start_time")
    ordered_task_names = get_ordered_task_names(data, order_by=order_by)

    ntasks = len(ordered_task_names)
    if ntasks == 0:
        print("No tasks found in the data.")
        return ""

    fig, ax = plt.subplots(figsize=(max(int(1.5 * ntasks), 6), 6))
    sns.barplot(
        data=grouped_data,
        x="task_name",
        y="mean",
        order=ordered_task_names,
        capsize=0.1,
        ax=ax,
    )
    # annotate the mean values on top of the bars
    for task_name in ordered_task_names:
        mean_value = grouped_data[grouped_data["task_name"] == task_name][
            "mean"
        ].values[0]
        std_value = grouped_data[grouped_data["task_name"] == task_name]["std"].values[
            0
        ]
        ax.text(
            x=task_name,
            y=mean_value + std_value + 0.1,
            s=f"{mean_value:.2f} ± {std_value:.2f}",
            ha="center",
            va="bottom",
        )
    ax.set_xticklabels(ax.get_xticklabels(), rotation=45, ha="right")
    ax.set_title(f"End-to-End Delay Distribution - {tag}")
    ax.set_xlabel("Task Name")
    ax.set_ylabel("Mean Delay (s)")

    log_scale = kwargs.get("log_scale", False)
    if log_scale:
        ax.set_yscale("log")
    if not log_scale:
        # Add error bars
        plt.errorbar(
            x=grouped_data["task_name"],
            y=grouped_data["mean"],
            yerr=grouped_data["std"],
            fmt="none",
            capsize=5,
            color="black",
        )
    plt.ylim(bottom=0)

    # Save the plot
    plot_path = os.path.join(save_dir, f"e2e_delay_bars_{tag}.png")
    if kwargs.get("merge_task", False):
        plot_path = plot_path.replace(".png", "_merged.png")
    if log_scale:
        plot_path = plot_path.replace(".png", "_log.png")
    plt.tight_layout()
    plt.savefig(plot_path)
    plt.close()

    print(f"Plot saved to {plot_path}")
    return plot_path


def plot_task_delay_cdf(
    data: DataFrame,
    tag: str = "test_agent",
    save_dir: Optional[str] = "./plots",
    **kwargs: Dict[str, str],
) -> str:
    """
    Plot the cumulative distribution function (CDF) of end-to-end delays for each task.

    Args:
        data: DataFrame containing delay data.
        tag: Tag for the plot title.
        save_dir: Directory to save the plot.
        **kwargs: Additional keyword arguments for customization.

    Returns:
        Path to the saved plot image.
    """
    print(f"Plotting task delay CDF for tag: {tag}")
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    # Shorten task names for better readability
    data = shorten_task_name(data)

    # Group by task name and calculate CDF of delays
    grouped_data = (
        data.groupby("task_name")["delay"].apply(lambda x: np.sort(x)).reset_index()
    )

    order_by = kwargs.get("order_by", "start_time")
    ordered_task_names = get_ordered_task_names(data, order_by=order_by)

    fig, ax = plt.subplots(figsize=(12, 6))
    # for _, row in grouped_data.iterrows():
    #     sns.ecdfplot(row["delay"], label=row["task_name"], ax=ax)
    for task_name in ordered_task_names:
        task_data = grouped_data[grouped_data["task_name"] == task_name][
            "delay"
        ].values[0]
        sns.ecdfplot(task_data, label=task_name, ax=ax)

    ax.set_title(f"End-to-End Delay CDF - {tag}")
    ax.set_xlabel("Delay (s)")
    ax.set_ylabel("Cumulative Probability")
    ax.legend(title="Task Name", bbox_to_anchor=(1.05, 1), loc="upper left")

    log_scale = kwargs.get("log_scale", False)
    if log_scale:
        ax.set_xscale("log")

    # Save the plot
    plot_path = os.path.join(save_dir, f"e2e_delay_cdf_{tag}.png")
    if kwargs.get("merge_task", False):
        plot_path = plot_path.replace(".png", "_merged.png")
    if log_scale:
        plot_path = plot_path.replace(".png", "_log.png")
    plt.tight_layout()
    plt.savefig(plot_path)
    plt.close()

    print(f"CDF plot saved to {plot_path}")
    return plot_path


def plot_task_delay_hist(
    data: DataFrame,
    tag: str = "test_agent",
    save_dir: Optional[str] = "./plots",
    **kwargs: Dict[str, str],
) -> str:
    """
    Plot the histogram of end-to-end delays for each task.

    Args:
        data: DataFrame containing delay data.
        tag: Tag for the plot title.
        save_dir: Directory to save the plot.
        **kwargs: Additional keyword arguments for customization.

    Returns:
        Path to the saved plot image.
    """
    print(f"Plotting task delay histogram for tag: {tag}")
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    # Shorten task names for better readability
    data = shorten_task_name(data)

    order_by = kwargs.get("order_by", "start_time")
    ordered_task_names = get_ordered_task_names(data, order_by=order_by)

    n_tasks = len(ordered_task_names)
    fig, axes, nrows, ncols = get_fig(n_tasks)

    log_scale = kwargs.get("log_scale", False)

    for i, task_name in enumerate(ordered_task_names):
        task_data = data[data["task_name"] == task_name]["delay"]

        sns.histplot(
            task_data,
            bins=30,
            kde=True,
            ax=axes[i],
            stat="density",
            color=sns.color_palette()[i % len(sns.color_palette())],
        )
        # add vertical line for mean and median, p90 and p95
        mean_delay = task_data.mean()
        median_delay = task_data.median()
        p90_delay = task_data.quantile(0.9)
        p95_delay = task_data.quantile(0.95)
        axes[i].axvline(
            mean_delay, color="red", linestyle="--", label=f"Mean: {mean_delay:.2f}s"
        )
        axes[i].axvline(
            median_delay,
            color="green",
            linestyle="--",
            label=f"Median: {median_delay:.2f}s",
        )
        axes[i].axvline(
            p90_delay, color="orange", linestyle="--", label=f"P90: {p90_delay:.2f}s"
        )
        axes[i].axvline(
            p95_delay, color="purple", linestyle="--", label=f"P95: {p95_delay:.2f}s"
        )
        if log_scale:
            axes[i].set_xscale("log")
            axes[i].set_yscale("log")
        axes[i].set_title(f"Delay Histogram - {task_name}")
        axes[i].set_xlabel("Delay (s)")
        axes[i].set_ylabel("Density")
        axes[i].grid(True)
        axes[i].legend()
    # Hide any unused subplots
    for j in range(i + 1, len(axes)):
        axes[j].axis("off")
    plt.tight_layout()
    plt.suptitle(f"End-to-End Delay Histogram - {tag}", y=1.02)
    plot_path = os.path.join(save_dir, f"e2e_delay_hist_{tag}.png")
    if kwargs.get("merge_task", False):
        plot_path = plot_path.replace(".png", "_merged.png")
    if log_scale:
        plot_path = plot_path.replace(".png", "_log.png")
    plt.savefig(plot_path)
    plt.close()
    print(f"Histogram plot saved to {plot_path}")
    return plot_path


def plot_task_route_delay(
    data: DataFrame,
    tag: str = "test_agent",
    plot_type: str = "bar",
    save_dir: Optional[str] = "./plots",
    **kwargs: Dict[str, str],
) -> str:
    """
    Plot the end-to-end delay distribution for each task and for each route in the data.
    Each task can have multiple routes, and the plot will show the mean delay for each route.
    Each task will be plotted separately in a subplot.

    Args:
        data: DataFrame containing delay data with route information.
        tag: Tag for the plot title.
        plot_type: Type of plot to generate ('bar', 'cdf', 'hist').
        save_dir: Directory to save the plot.
        **kwargs: Additional keyword arguments for customization.

    Returns:
        Path to the saved plot image.
    """
    print(f"Plotting task route delay with plot type: {plot_type}")
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    # Shorten task names for better readability
    data = shorten_task_name(data)
    order_by = kwargs.get("order_by", "start_time")
    ordered_task_names = get_ordered_task_names(data, order_by=order_by)
    log_scale = kwargs.get("log_scale", False)
    merge_task = kwargs.get("merge_task", False)

    ntasks = len(ordered_task_names)
    if ntasks == 0:
        print("No tasks found in the data.")
        return ""

    n_tasks = len(ordered_task_names)
    fig, axes, nrows, ncols = get_fig(n_tasks)

    for i, task_name in enumerate(ordered_task_names):
        task_data = data[data["task_name"] == task_name]
        nroutes = task_data["route"].nunique()
        if nroutes == 0:
            print(f"No routes found for task: {task_name}")
            continue
        if nroutes >= 10:
            print(
                f"Warning: Task {task_name} has {nroutes} routes, which may clutter the plot."
            )
        if plot_type == "bar":
            sns.barplot(
                data=task_data,
                x="route",
                y="delay",
                ax=axes[i],
                capsize=0.1,
                order=task_data["route"].value_counts().index,  # sort by frequency
            )
            axes[i].set_title(f"Route Delay - {task_name} ({nroutes} routes)")
            axes[i].set_xlabel("Route")
            axes[i].set_ylabel("Mean Delay (s)")
            axes[i].set_ylim(bottom=0)
            if log_scale:
                axes[i].set_yscale("log")
        elif plot_type == "cdf":
            sns.ecdfplot(data=task_data, x="delay", hue="route", ax=axes[i])
            axes[i].set_title(f"Route Delay CDF - {task_name} ({nroutes} routes)")
            axes[i].set_xlabel("Delay (s)")
            axes[i].set_ylabel("Cumulative Probability")
        elif plot_type == "hist":
            sns.histplot(
                data=task_data,
                x="delay",
                hue="route",
                bins=30,
                kde=True,
                ax=axes[i],
                stat="density",
            )
            axes[i].set_title(f"Route Delay Histogram - {task_name} ({nroutes} routes)")
            axes[i].set_xlabel("Delay (s)")
            axes[i].set_ylabel("Density")
            if log_scale:
                axes[i].set_xscale("log")
                axes[i].set_yscale("log")
        elif plot_type == "box":
            sns.boxplot(
                data=task_data,
                x="route",
                y="delay",
                ax=axes[i],
                order=task_data["route"].unique(),
            )
            axes[i].set_title(f"Route Delay Boxplot - {task_name} ({nroutes} routes)")
            axes[i].set_xlabel("Route")
            axes[i].set_ylabel("Delay (s)")
        elif plot_type == "stats-only":
            stats = (
                task_data.groupby("route")["delay"]
                .agg(["mean", "std", "count"])
                .reset_index()
            )
            stats = stats.sort_values(by="count", ascending=False)
            axes[i].bar(
                stats["route"],
                stats["mean"],
                yerr=stats["std"],
                capsize=5,
                color=sns.color_palette()[i % len(sns.color_palette())],
                alpha=0.8,
            )
            axes[i].set_title(f"Route Delay Stats - {task_name} ({nroutes} routes)")
            axes[i].set_xlabel("Route")
            axes[i].set_ylabel("Mean Delay (s)")
            axes[i].set_ylim(bottom=0)
            if log_scale:
                axes[i].set_yscale("log")
            # save stats to a csv file
            stats_save_path = os.path.join(
                save_dir, ".cache", f"route_delay_stats_{task_name}_{tag}.csv"
            )
            stats.to_csv(stats_save_path, index=False)
            print(f"Route delay stats saved to {stats_save_path}")
        else:
            print(
                f"Unsupported plot type: {plot_type}. Supported types are: 'bar', 'cdf', 'hist', 'box'."
            )
            continue

        # axes[i].legend(title="Route", bbox_to_anchor=(1.05, 1), loc="upper left")

    # Hide any unused subplots
    for j in range(i + 1, len(axes)):
        axes[j].axis("off")
    plt.tight_layout()
    plt.suptitle(
        f"End-to-End Delay by Route - {tag} ({data['route'].nunique()} routes)", y=1.02
    )
    plot_path = os.path.join(save_dir, f"e2e_route_delay_{plot_type}_{tag}.png")
    if merge_task:
        plot_path = plot_path.replace(".png", "_merged.png")
    if log_scale:
        plot_path = plot_path.replace(".png", "_log.png")
    plt.savefig(plot_path)
    plt.close()
    print(f"Route delay plot saved to {plot_path}")
    return plot_path


def plot_bottleneck_distribution(
    data: DataFrame,
    tag: str = "test_agent",
    save_dir: Optional[str] = "./plots",
    **kwargs: Dict[str, str],
) -> str:
    """
    Different request_id may have different bottleneck tasks.
    Plot the distribution of bottleneck tasks.

    Args:
        data: DataFrame containing delay data.
        tag: Tag for the plot title.
        save_dir: Directory to save the plot.
        **kwargs: Additional keyword arguments for customization.

    Returns:
        Path to the saved plot image.
    """
    print(f"Plotting bottleneck distribution for tag: {tag}")
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    # Shorten task names for better readability
    data = shorten_task_name(data)

    # for each request, get the bottleneck task
    bottleneck_data = (
        data.groupby("request_id")["delay"].idxmax().reset_index(drop=True)
    )
    bottleneck_tasks = (
        data.loc[bottleneck_data, "task_name"].value_counts().reset_index()
    )
    bottleneck_tasks.columns = ["task_name", "count"]
    total_count = bottleneck_tasks["count"].sum()
    bottleneck_tasks["percentage"] = 100.0 * bottleneck_tasks["count"] / total_count
    bottleneck_tasks = bottleneck_tasks.sort_values(by="count", ascending=False)
    if bottleneck_tasks.empty:
        print("No bottleneck tasks found in the data.")
        return ""
    n_tasks = len(bottleneck_tasks)
    fig, ax = plt.subplots(figsize=(max(int(1.5 * n_tasks), 6), 6))
    sns.barplot(
        data=bottleneck_tasks,
        x="task_name",
        y="percentage",
        ax=ax,
        capsize=0.1,
    )
    # annotate the percentage values on top of the bars
    for index, row in bottleneck_tasks.iterrows():
        ax.text(
            x=row["task_name"],
            y=row["percentage"] + 0.5,
            s=f"{row['percentage']:.2f}% ({row['count']})",
            ha="center",
            va="bottom",
        )
    ax.set_title(f"Bottleneck Task Distribution - {tag}")
    ax.set_xlabel("Task Name")
    ax.set_ylabel("Percentage")
    ax.set_xticklabels(ax.get_xticklabels(), rotation=45, ha="right")
    ax.set_ylim(bottom=0, top=min(110, bottleneck_tasks["percentage"].max() + 10))
    log_scale = kwargs.get("log_scale", False)
    if log_scale:
        ax.set_yscale("log")
    # Save the plot
    plot_path = os.path.join(save_dir, f"bottleneck_distribution_{tag}.png")
    if kwargs.get("merge_task", False):
        plot_path = plot_path.replace(".png", "_merged.png")
    if log_scale:
        plot_path = plot_path.replace(".png", "_log.png")
    plt.tight_layout()
    plt.savefig(plot_path)
    plt.close()
    print(f"Bottleneck distribution plot saved to {plot_path}")
    return plot_path


def plot_bottleneck_distribution_each_route(
    data: DataFrame,
    tag: str = "test_agent",
    save_dir: Optional[str] = "./plots",
    **kwargs: Dict[str, str],
) -> str:
    """
    Plot the bottleneck task distribution for each route.

    Args:
        data: DataFrame containing delay data with route information.
        tag: Tag for the plot title.
        save_dir: Directory to save the plot.
        **kwargs: Additional keyword arguments for customization.

    Returns:
        Path to the saved plot image.
    """
    print(f"Plotting bottleneck distribution for each route with tag: {tag}")
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    # Shorten task names for better readability
    data = shorten_task_name(data)

    # Group by route and request_id, then find the bottleneck task
    bottleneck_data = (
        data.groupby("request_id")["delay"].idxmax().reset_index(drop=True)
    )
    bottleneck_tasks = (
        data.loc[bottleneck_data, ["route", "task_name"]]
        .value_counts()
        .reset_index(name="count")
    )
    nroutes = bottleneck_tasks["route"].nunique()
    print(f"nroutes={nroutes}", bottleneck_tasks)
    figsize = (
        min(20, max((int(1.5 * nroutes), 6))),
        min(12, max((int(0.5 * nroutes), 6))),
    )
    fig, ax = plt.subplots(figsize=figsize)
    all_possible_bottlenecks = bottleneck_tasks["task_name"].unique()
    # We plot a stacked bars for each route in the ax
    bottom = np.zeros(nroutes)
    for i, task_name in enumerate(all_possible_bottlenecks):
        task_data = bottleneck_tasks[bottleneck_tasks["task_name"] == task_name]
        if task_data.empty:
            continue
        # Create a bar for each route
        route_counts = task_data.set_index("route")["count"]
        route_counts = route_counts.sort_index()
        route_counts = route_counts.reindex(
            bottleneck_tasks["route"].unique(), fill_value=0
        )
        ax.bar(
            route_counts.index,
            route_counts.values,
            bottom=bottom,
            label=task_name,
        )
        bottom += route_counts.values
    # ax.legend(title="Bottleneck Task", bbox_to_anchor=(1.05, 1), loc="upper left")
    ax.legend(title="Bottleneck Task")
    ax.set_title(f"Bottleneck Task Distribution by Route - {tag}")
    ax.set_xlabel("Route")
    ax.set_ylabel("Count")
    ax.set_xticklabels(ax.get_xticklabels(), rotation=45, ha="right")
    log_scale = kwargs.get("log_scale", False)
    if log_scale:
        ax.set_yscale("log")
    # Save the plot
    plot_path = os.path.join(save_dir, f"bottleneck_distribution_route_{tag}.png")
    if kwargs.get("merge_task", False):
        plot_path = plot_path.replace(".png", "_merged.png")
    if log_scale:
        plot_path = plot_path.replace(".png", "_log.png")
    plt.savefig(plot_path)
    plt.close()
    print(f"Bottleneck distribution plot saved to {plot_path}")
    return plot_path


def multi_plot_task_delay_cdf(
    multiple_data: Dict[str, DataFrame],
    tag: str = "test_agent",
    save_dir: Optional[str] = "./plots",
    **kwargs: Dict[str, str],
):
    """
    Plot the cumulative distribution function (CDF) of end-to-end delays for each task across multiple datasets.

    Args:
        multiple_data: Dictionary of DataFrames containing delay data for different tags.
        tag: Tag for the plot title.
        save_dir: Directory to save the plot.
        **kwargs: Additional keyword arguments for customization.

    Returns:
        Path to the saved plot image.
    """
    print(f"Plotting task delay CDF for multiple datasets with tag: {tag}")
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    # Shorten task names for better readability
    name_map = get_short_name_map(pd.concat(multiple_data.values(), ignore_index=True))
    if name_map:
        for key, data in multiple_data.items():
            data["task_name"] = data["task_name"].map(name_map)
            print(f"Shortened task names for group: {key}")
    else:
        print("No task names to shorten.")

    # Get ordered task names based on the first occurrence in the data
    order_by = kwargs.get("order_by", "start_time")
    ordered_task_names = []
    for key, data in multiple_data.items():
        ordered_task_names.extend(get_ordered_task_names(data, order_by=order_by))
    ordered_task_names = list(set(ordered_task_names))  # Remove duplicates

    n_tasks = len(ordered_task_names)
    if n_tasks == 0:
        print("No tasks found in the data.")
        return ""
    fig, axes, nrows, ncols = get_fig(n_tasks)
    ngroups = len(multiple_data)
    group_colors = sns.color_palette("husl", ngroups)

    log_scale = kwargs.get("log_scale", False)
    for i, task_name in enumerate(ordered_task_names):
        for j, (group, data) in enumerate(multiple_data.items()):
            task_data = data[data["task_name"] == task_name]
            if task_data.empty:
                print(f"No data found for task: {task_name} in group: {group}")
                continue
            sns.ecdfplot(
                task_data["delay"],
                label=group,
                ax=axes[i],
                color=group_colors[j],
            )
        axes[i].set_title(f"Task Delay CDF - {task_name}")
        axes[i].set_xlabel("Delay (s)")
        axes[i].set_ylabel("Cumulative Probability")
        axes[i].legend(title="RPS", bbox_to_anchor=(1.05, 1), loc="upper left")
        if log_scale:
            axes[i].set_xscale("log")
    # Hide any unused subplots
    for j in range(i + 1, len(axes)):
        axes[j].axis("off")
    plt.tight_layout()
    plt.suptitle(f"End-to-End Delay CDF - {tag}", y=1.02)
    plot_path = os.path.join(save_dir, f"e2e_delay_cdf_multiple_{tag}.png")
    if kwargs.get("merge_task", False):
        plot_path = plot_path.replace(".png", "_merged.png")
    if log_scale:
        plot_path = plot_path.replace(".png", "_log.png")
    plt.savefig(plot_path)
    plt.close()
    print(f"CDF plot saved to {plot_path}")
    return plot_path


def multi_plot_task_latency_throughput(
    multiple_data: Dict[str, DataFrame],
    tag: str = "test_agent",
    save_dir: Optional[str] = "./plots",
    **kwargs: Dict[str, str],
):
    """
    Plot the relationship between latency and throughput for each task across multiple datasets.

    Args:
        multiple_data: Dictionary of DataFrames containing delay data for different tags.
        tag: Tag for the plot title.
        save_dir: Directory to save the plot.
        **kwargs: Additional keyword arguments for customization.

    Returns:
        Path to the saved plot image.
    """
    print(f"Plotting task latency and throughput for multiple datasets with tag: {tag}")
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    # Shorten task names for better readability
    name_map = get_short_name_map(pd.concat(multiple_data.values(), ignore_index=True))
    if name_map:
        for key, data in multiple_data.items():
            data["task_name"] = data["task_name"].map(name_map)
            print(f"Shortened task names for group: {key}")
    else:
        print("No task names to shorten.")

    # Get ordered task names based on the first occurrence in the data
    order_by = kwargs.get("order_by", "start_time")
    ordered_task_names = []
    for key, data in multiple_data.items():
        ordered_task_names.extend(get_ordered_task_names(data, order_by=order_by))
    ordered_task_names = list(set(ordered_task_names))  # Remove duplicates

    n_tasks = len(ordered_task_names)
    if n_tasks == 0:
        print("No tasks found in the data.")
        return ""
    fig, axes, nrows, ncols = get_fig(n_tasks)
    ngroups = len(multiple_data)
    group_colors = sns.color_palette("husl", ngroups)

    log_scale = kwargs.get("log_scale", False)
    merge_task = kwargs.get("merge_task", False)
    multi_stats = {
        key: get_data_statistics(data, by="task_name")
        for key, data in multiple_data.items()
    }

    for i, task_name in enumerate(ordered_task_names):
        point_data = []
        for j, (group, stats) in enumerate(multi_stats.items()):
            task_stats = stats[stats["task_name"] == task_name]
            if task_stats.empty:
                print(f"No stats found for task: {task_name} in group: {group}")
                continue
            point_data.append(
                {
                    "group": group,
                    "latency": task_stats["delay_mean"].values[0],
                    "throughput": task_stats["throughput"].values[0],
                    "color": group_colors[j],
                }
            )
        if not point_data:
            print(f"No data points found for task: {task_name}. Skipping.")
            continue
        # Create a DataFrame for the points
        point_df = pd.DataFrame(point_data)

        # Plot the points
        sns.scatterplot(
            data=point_df,
            x="latency",
            y="throughput",
            hue="group",
            palette=group_colors,
            ax=axes[i],
        )

        axes[i].set_title(f"Latency vs Throughput - {task_name}")
        axes[i].set_xlabel("Latency (s)")
        axes[i].set_ylabel("Throughput (req/s)")
        axes[i].legend(title="RPS", bbox_to_anchor=(1.05, 1), loc="upper left")
        if log_scale:
            axes[i].set_xscale("log")
            axes[i].set_yscale("log")
    # Hide any unused subplots
    for j in range(i + 1, len(axes)):
        axes[j].axis("off")
    plt.tight_layout()
    plt.suptitle(f"End-to-End Latency vs Throughput - {tag}", y=1.02)
    plot_path = os.path.join(save_dir, f"e2e_latency_throughput_multiple_{tag}.png")
    if merge_task:
        plot_path = plot_path.replace(".png", "_merged.png")
    if log_scale:
        plot_path = plot_path.replace(".png", "_log.png")
    plt.savefig(plot_path)
    plt.close()
    print(f"Latency vs Throughput plot saved to {plot_path}")
    return plot_path


def get_args():
    import argparse

    parser = argparse.ArgumentParser(description="Plot end-to-end delay distribution.")
    parser.add_argument(
        "--instance_dirs",
        type=str,
        nargs="+",
        required=True,
        help="List of instance directories containing delay data.",
    )
    parser.add_argument(
        "--instance_groups",
        type=str,
        nargs="+",
        default=None,
        help="List of groups for the instance directories. If None, all instances share the same group 'default'.",
    )
    parser.add_argument(
        "--tag", type=str, default="test_agent", help="Tag for the plot title."
    )
    parser.add_argument(
        "--log_scale",
        action="store_true",
        help="Use logarithmic scale for the plot",
    )
    parser.add_argument(
        "--no_cache",
        action="store_true",
        help="If set, will not use cached data and will parse the module IO data again",
    )
    parser.add_argument(
        "--include_sources",
        type=str,
        nargs="*",
        default=None,
        help="List of sources to include in the plot. If None, all sources will be included.",
    )
    parser.add_argument(
        "--exclude_sources",
        type=str,
        nargs="*",
        default=None,
        help="List of sources to exclude from the plot. If None, no sources will be excluded.",
    )
    parser.add_argument(
        "--include_delay_types",
        type=str,
        nargs="*",
        default=None,
        help="List of delay types to include in the plot. If None, all delay types will be included.",
    )
    parser.add_argument(
        "--exclude_delay_types",
        type=str,
        nargs="*",
        default=None,
        help="List of delay types to exclude from the plot. If None, no delay types will be excluded.",
    )
    parser.add_argument(
        "--include_tasks",
        type=str,
        nargs="*",
        default=None,
        help="List of task names to include in the plot. If None, all tasks will be included.",
    )
    parser.add_argument(
        "--exclude_tasks",
        type=str,
        nargs="*",
        default=None,
        help="List of task names to exclude from the plot. If None, no tasks will be excluded.",
    )
    parser.add_argument(
        "--exclude_tasks_in_tree",
        type=str,
        nargs="*",
        default=None,
        help="List of task names to exclude from the tree structure. If None, no tasks will be excluded.",
    )
    parser.add_argument(
        "--merge_task",
        action="store_true",
        help="If set, will merge tasks in the delay data.",
    )
    parser.add_argument(
        "--include_route",
        action="store_true",
        help="If set, will include the route information in the delay data.",
    )
    parser.add_argument(
        "--task_groups",
        type=str,
        nargs="*",
        default=None,
        help="List of task groups to include in the plot. Should follow the pattern {task_name}:{group_name} If None, default group will be used.",
    )
    parser.add_argument(
        "--task_default_group",
        type=str,
        default=None,
        help="Default group for tasks if not specified in task_groups. If None, group_name = task_name",
    )
    parser.add_argument(
        "--save_dir",
        type=str,
        default="./plots/delay",
        help="Directory to save the plot.",
    )

    return parser.parse_args()


def filter_tasks(
    data: DataFrame,
    include_sources: Optional[List[str]] = None,
    exclude_sources: Optional[List[str]] = None,
    include_delay_types: Optional[List[str]] = None,
    exclude_delay_types: Optional[List[str]] = None,
    include_tasks: Optional[List[str]] = None,
    exclude_tasks: Optional[List[str]] = None,
) -> DataFrame:
    """
    Filter the DataFrame based on included and excluded task names.

    Args:
        data: DataFrame containing delay data.
        include_tasks: List of task names to include. If None, all tasks are included.
        exclude_tasks: List of task names to exclude. If None, no tasks are excluded.

    Returns:
        Filtered DataFrame.
    """
    print(f"Filtering tasks in the data with {len(data)} records.")
    if include_sources is not None:
        print(f"Including only sources: {include_sources}")
        data = data[data["source"].isin(include_sources)]
    if exclude_sources is not None:
        print(f"Excluding sources: {exclude_sources}")
        data = data[~data["source"].isin(exclude_sources)]
    print(f"Filtered data contains {len(data)} records after applying source filters.")

    if include_delay_types is not None:
        print(f"Including only delay types: {include_delay_types}")
        try:
            data = data[data["delay_type"].isin(include_delay_types)]
        except KeyError:
            print(data.columns)
            raise KeyError(
                "The 'delay_type' column is not present in the DataFrame. "
                "Please ensure that the DataFrame contains this column."
            )
    if exclude_delay_types is not None:
        print(f"Excluding delay types: {exclude_delay_types}")
        data = data[~data["delay_type"].isin(exclude_delay_types)]
    print(
        f"Filtered data contains {len(data)} records after applying delay type filters."
    )

    if include_tasks is not None:
        print(f"Including only tasks: {include_tasks}")
        task_names = data["task_name"].unique()
        selected_tasks = [
            task for task in task_names if any(name in task for name in include_tasks)
        ]
        if not selected_tasks:
            print(f"No matching tasks found in the data for: {include_tasks}")
            return
        data = data[data["task_name"].isin(selected_tasks)]
    if exclude_tasks is not None:
        print(f"Excluding tasks: {exclude_tasks}")
        task_names = data["task_name"].unique()
        selected_tasks = [
            task
            for task in task_names
            if not any(name in task for name in exclude_tasks)
        ]
        if not selected_tasks:
            print(f"No tasks left after exclusion: {exclude_tasks}")
            return
        data = data[data["task_name"].isin(selected_tasks)]
    print(f"Filtered data contains {len(data)} records after applying task filters.")
    return data


def get_merged_delay(start_times: List[float], end_times: List[float]) -> float:
    """The range may overlap"""
    ranges = sorted(zip(start_times, end_times))
    total_delay = 0.0
    current_start, current_end = ranges[0]
    for start, end in ranges[1:]:
        if start <= current_end:
            current_end = max(current_end, end)
        else:
            total_delay += current_end - current_start
            current_start, current_end = start, end
    total_delay += current_end - current_start
    return total_delay


def load_data(
    instance_dirs: List[str],
    tag: str,
    save_dir: str,
    merge_task: bool = False,
    include_route: bool = False,
    exclude_tasks_in_tree: List[str] = None,
    no_cache: bool = False,
    **kwargs,
) -> DataFrame:
    # Parse delay data
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    if not os.path.exists(os.path.join(save_dir, ".cache")):
        os.makedirs(os.path.join(save_dir, ".cache"))

    data_save_path = os.path.join(save_dir, ".cache", f"{tag}_delay_data.csv")
    if merge_task:
        data_save_path = os.path.join(
            save_dir, ".cache", f"{tag}_merged_delay_data.csv"
        )
    if include_route:
        data_save_path = os.path.join(
            save_dir, ".cache", f"{tag}_delay_data_with_route.csv"
        )
    if os.path.exists(data_save_path) and not no_cache:
        print(f"Data already exists at {data_save_path}. Loading from file.")
        data = pd.read_csv(data_save_path)
        if len(data) == 0:
            print(f"No data found for tag: {tag}")
            return
    else:
        print(f"Data not found at {data_save_path}. Parsing delay data.")
        result_df_dict, execution_tree_dict = parse_delay_data(instance_dirs, tag)

        if include_route:
            print(
                f"Including route information in the delay data. {exclude_tasks_in_tree} tasks are excluded"
            )
            route_count = {}
            for request_id, df in result_df_dict.items():
                df["request_id"] = request_id
                tree = execution_tree_dict[request_id]
                tasks = tree.get_all_tasks()
                route_exclude_tasks = []
                if exclude_tasks_in_tree is not None:
                    for task_str in exclude_tasks_in_tree:
                        for task_name in tasks:
                            if task_str in task_name:
                                route_exclude_tasks.append(task_name)
                route = tree.get_structure(indent=2, exclude_tasks=route_exclude_tasks)
                route_hash = hashlib.md5(route.encode()).hexdigest()
                if route_hash not in route_count:
                    route_size = len(route.split("\n")) - 1
                    route_count[route_hash] = {
                        "count": 0,
                        "route_size": route_size,
                        "route": route,
                    }
                route_count[route_hash]["count"] += 1
                df["route"] = route_hash
            for i, (key, value) in enumerate(route_count.items()):
                value["route_id"] = i

            # update the route in the DataFrame to route_id
            for request_id, df in result_df_dict.items():
                df["route"] = df["route"].map(
                    {key: value["route_id"] for key, value in route_count.items()}
                )
            # Save route count to a JSON file
            route_count_path = os.path.join(
                save_dir, ".cache", f"{tag}_route_count.json"
            )
            with open(route_count_path, "w") as f:
                json.dump(route_count, f, indent=4)

        if merge_task:
            print("Merging tasks in the delay data.")
            merged_data = []
            for request_id, df in tqdm(result_df_dict.items(), desc="Merging tasks"):
                # for task_name, group in df.groupby(["source", "task_name"]):
                for (source, task_name), group in df.groupby(["source", "task_name"]):
                    if len(group) == 0:
                        continue
                    item = {
                        "request_id": request_id,
                        "source": source,
                        "task_name": task_name,
                        "route": group["route"].iloc[0] if include_route else None,
                        "start_time": group["start_time"].min(),
                        "end_time": group["end_time"].max(),
                        "count": len(group),
                        "delay": get_merged_delay(
                            group["start_time"].tolist(),
                            group["end_time"].tolist(),
                        ),
                        "delay_type": group["delay_type"].iloc[0],
                    }
                    merged_data.append(item)
            data = pd.DataFrame(merged_data)
            data = data.sort_values(by="start_time").reset_index(drop=True)
        else:
            # Convert to DataFrame for plotting
            data = pd.concat(result_df_dict.values(), ignore_index=True)

        # save concatenated dataframe
        data.to_csv(data_save_path, index=False)

    if len(data) == 0:
        print(f"No data found for tag: {tag}")
        return data

    data = filter_tasks(
        data,
        include_sources=kwargs.get("include_sources", None),
        exclude_sources=kwargs.get("exclude_sources", None),
        include_delay_types=kwargs.get("include_delay_types", None),
        exclude_delay_types=kwargs.get("exclude_delay_types", None),
        include_tasks=kwargs.get("include_tasks", None),
        exclude_tasks=kwargs.get("exclude_tasks", None),
    )

    task_groups: List[str] = kwargs.get("task_groups", None)
    default_group: str = kwargs.get("default_group", None)
    if task_groups is not None:
        task_group_map = {}
        for group in task_groups:
            if ":" in group:
                task_name, group_name = group.split(":", 1)
                task_group_map[task_name] = group_name
            else:
                print(
                    f"Invalid task group format: {group}. Expected 'task_name:group_name'."
                )
        data = assign_group_to_tasks(data, task_group_map, default_group)

    return data


def assign_group_to_tasks(
    data: DataFrame,
    task_group_map: Optional[Dict[str, str]] = None,
    default_group: Optional[str] = None,
) -> DataFrame:
    """
    Assign a group to each task based on the provided task_groups mapping.

    Args:
        data: DataFrame containing delay data.
        task_groups: Dictionary mapping task names to their respective groups.

    Returns:
        DataFrame with an additional 'group' column indicating the group of each task.
    """
    if task_group_map is None:
        return data

    task_names = data["task_name"].unique()
    name_map = {}
    for task_name in task_names:
        name_map[task_name] = default_group if default_group else task_name
        for tsk, group in task_group_map.items():
            if tsk in task_name:
                name_map[task_name] = group
                break

    print(f"Assigning groups to tasks based on provided mapping.")
    data["group"] = data["task_name"].map(name_map)

    return data


def get_data_statistics(
    data: DataFrame,
    by: str = "task_name",
) -> DataFrame:
    """Calculate statistics for the delay data grouped by a specified column.
    Args:
        data: DataFrame containing delay data.
        by: Column name to group by (default is 'task_name').

    Return: Statistics of each group, including
        delay_count, delay_mean, delay_median, delay_std, delay_p90, delay_p95, delay_p99, delay_min, delay_max,
        first_start_time, first_end_time, last_start_time, last_end_time.
    """

    print(f"Calculating statistics for the delay data grouped by {by}.")
    if by not in data.columns:
        raise ValueError(f"Column '{by}' not found in the DataFrame.")

    stats = (
        data.groupby(by)
        .agg(
            delay_count=("delay", "count"),
            delay_mean=("delay", "mean"),
            delay_median=("delay", "median"),
            delay_std=("delay", "std"),
            delay_p90=("delay", lambda x: x.quantile(0.9)),
            delay_p95=("delay", lambda x: x.quantile(0.95)),
            delay_p99=("delay", lambda x: x.quantile(0.99)),
            delay_min=("delay", "min"),
            delay_max=("delay", "max"),
            first_start_time=("start_time", "min"),
            first_end_time=("end_time", "min"),
            last_start_time=("start_time", "max"),
            last_end_time=("end_time", "max"),
        )
        .reset_index()
    )
    stats = stats.sort_values(by="delay_mean", ascending=False).reset_index(drop=True)
    stats["throughput"] = stats["delay_count"] / (
        stats["last_end_time"] - stats["first_start_time"]
    )

    return stats


def single_exp_plot(args):
    log_scale = args.log_scale
    instance_dirs = args.instance_dirs
    tag = args.tag
    save_dir = args.save_dir
    merge_task = args.merge_task
    include_route = args.include_route
    exclude_tasks_in_tree = args.exclude_tasks_in_tree
    no_cache = args.no_cache
    include_sources = args.include_sources
    exclude_sources = args.exclude_sources
    include_delay_types = args.include_delay_types
    exclude_delay_types = args.exclude_delay_types
    include_tasks = args.include_tasks
    exclude_tasks = args.exclude_tasks
    print(f"Processing instance directories: {instance_dirs} with tag: {tag}")
    df = load_data(
        instance_dirs=instance_dirs,
        tag=tag,
        save_dir=save_dir,
        merge_task=merge_task,
        include_route=include_route,
        exclude_tasks_in_tree=exclude_tasks_in_tree,
        no_cache=no_cache,
        include_sources=include_sources,
        exclude_sources=exclude_sources,
        include_delay_types=include_delay_types,
        exclude_delay_types=exclude_delay_types,
        include_tasks=include_tasks,
        exclude_tasks=exclude_tasks,
        task_groups=args.task_groups,
        default_group=args.task_default_group,
    )
    if df.empty:
        print("No delay data found. Exiting.")
        sys.exit(0)

    # plot bootleneck distribution
    plot_bottleneck_distribution(
        df,
        tag=tag,
        save_dir=save_dir,
        log_scale=log_scale,
        merge_task=merge_task,
    )
    plot_bottleneck_distribution_each_route(
        df,
        tag=tag,
        save_dir=save_dir,
        log_scale=log_scale,
        merge_task=merge_task,
    )

    # sys.exit(0)

    # Plot the end-to-end delay distribution
    plot_task_delay_bars(
        df,
        tag,
        save_dir,
        order_by="mean",
        log_scale=log_scale,
        merge_task=merge_task,
    )

    # Plot the end-to-end delay CDF
    plot_task_delay_cdf(
        df,
        tag,
        save_dir,
        order_by="mean",
        log_scale=log_scale,
        merge_task=merge_task,
    )

    # Plot the end-to-end delay histogram
    plot_task_delay_hist(
        df,
        tag,
        save_dir,
        order_by="mean",
        log_scale=log_scale,
        merge_task=merge_task,
    )

    # Plot the end-to-end delay by route
    if include_route:
        plot_task_route_delay(
            df,
            tag,
            plot_type="bar",
            save_dir=save_dir,
            order_by="mean",
            log_scale=log_scale,
            merge_task=merge_task,
        )
        plot_task_route_delay(
            df,
            tag,
            plot_type="cdf",
            save_dir=save_dir,
            order_by="mean",
            log_scale=log_scale,
            merge_task=merge_task,
        )
        plot_task_route_delay(
            df,
            tag,
            plot_type="hist",
            save_dir=save_dir,
            order_by="mean",
            log_scale=log_scale,
            merge_task=merge_task,
        )
        plot_task_route_delay(
            df,
            tag,
            plot_type="stats-only",
            save_dir=save_dir,
            order_by="mean",
            log_scale=log_scale,
            merge_task=merge_task,
        )


def multiple_exps_plot(args):
    instance_groups = args.instance_groups
    instance_dirs = args.instance_dirs

    assert len(instance_groups) == len(instance_dirs)
    # group dirs by tags
    grouped_dirs = {}
    for i, group in enumerate(instance_groups):
        if group not in grouped_dirs:
            grouped_dirs[group] = []
        grouped_dirs[group].append(instance_dirs[i])

    group_data = {}
    for group, dirs in grouped_dirs.items():
        print(f"Processing instance directories: {dirs} with group: {group}")
        df = load_data(
            instance_dirs=dirs,
            tag=f"{args.tag}_{group}",
            save_dir=f"{args.save_dir}/{group}",
            merge_task=args.merge_task,
            include_route=args.include_route,
            exclude_tasks_in_tree=args.exclude_tasks_in_tree,
            no_cache=args.no_cache,
            include_sources=args.include_sources,
            exclude_sources=args.exclude_sources,
            include_delay_types=args.include_delay_types,
            exclude_delay_types=args.exclude_delay_types,
            include_tasks=args.include_tasks,
            exclude_tasks=args.exclude_tasks,
        )
        if df.empty:
            print(f"No delay data found for group: {group}. Skipping.")
            continue
        df["group"] = group  # Add group column for plotting
        group_data[group] = df
    df = pd.concat(group_data.values(), ignore_index=True)
    if df.empty:
        print("No delay data found across all groups. Exiting.")
        sys.exit(0)

    # Plot the cdf of each task across all groups
    multi_plot_task_delay_cdf(
        group_data,
        tag=args.tag,
        save_dir=args.save_dir,
        order_by="mean",
        log_scale=args.log_scale,
        merge_task=args.merge_task,
    )

    # Plot the latency vs throughput for each task across all groups
    multi_plot_task_latency_throughput(
        group_data,
        tag=args.tag,
        save_dir=args.save_dir,
        order_by="mean",
        log_scale=args.log_scale,
        merge_task=args.merge_task,
    )


if __name__ == "__main__":
    args = get_args()

    if args.instance_groups is None:
        single_exp_plot(args)
    else:
        multiple_exps_plot(args)
