# HuggingGPT Tool IO Size Analysis Script
python tool_io.py --tag hugginggpt \
    --module_dirs /mnt/data/agbench/results/hugginggpt/intermediate/requests \
    --protocal json_str \
    --include_tasks model_selection model_inference \
    --log_scale \
    --save_dir ./plots/tool_io/hgpt

# python tool_io.py --tag hugginggpt \
#     --module_dirs /mnt/data/agbench/results/hugginggpt/intermediate/requests \
#     --protocal json_str \
#     --save_dir ./plots/tool_io/hgpt

# python tool_io.py --tag hugginggpt \
#     --module_dirs /mnt/data/agbench/results/hugginggpt/intermediate/requests \
#     --protocal bytes \
#     --save_dir ./plots/tool_io/hgpt

# Deep Research Tool IO Size Analysis Script
python tool_io.py --tag drs \
    --module_dirs /mnt/data/agbench/results/drs/final/baseline/stage-1/drs_async-YDCDRS-OneByOne-0.0-202/results/server/intermediate/requests \
    --protocal json_str \
    --log_scale \
    --include_tasks search_with_queries_async split_texts_async embed_docs_async store_vdb_async embed_texts_async search_vdb_async clear_vdb_async \
    --exclude_tasks inner_embed_texts_async \
    --save_dir ./plots/tool_io/drs

# python tool_io.py --tag drs \
#     --module_dirs /mnt/data/agbench/results/drs/final/baseline/stage-1/drs_async-YDCDRS-OneByOne-0.0-202/results/server/intermediate/requests \
#     --protocal bytes \
#     --log_scale \
#     --include_tasks search_with_queries_async split_texts_async embed_docs_async store_vdb_async embed_texts_async search_vdb_async clear_vdb_async \
#     --exclude_tasks inner_embed_texts_async \
#     --save_dir ./plots/tool_io/drs

# python tool_io.py --tag drs \
#     --module_dirs /mnt/data/agbench/results/drs/final/baseline/stage-1/drs_async-YDCDRS-OneByOne-0.0-202/results/server/intermediate/requests \
#     --protocal json_str \
#     --log_scale \
#     --save_dir ./plots/tool_io/drs

# python tool_io.py --tag drs \
#     --module_dirs /mnt/data/agbench/results/drs/final/baseline/stage-1/drs_async-YDCDRS-OneByOne-0.0-202/results/server/intermediate/requests \
#     --protocal json_str \
#     --save_dir ./plots/tool_io/drs

# SWE Agent Tool IO Size Analysis Script
python tool_io.py --tag swe \
    --module_dirs /mnt/data/agbench/results/swe/swe-default/stage-1/data/intermediate/env_manager/requests \
    /mnt/data/agbench/results/swe/swe-default/stage-1/data/intermediate/server/requests \
    --protocal json_str \
    --log_scale \
    --save_dir ./plots/tool_io/swe

# python tool_io.py --tag swe-ds32B \
#     --module_dirs /mnt/data/agbench/results/swe/swe-ds32B/stage-1-v0/stage-1-batchx8/data/intermediate/env_manager/requests \
#     /mnt/data/agbench/results/swe/swe-ds32B/stage-1-v0/stage-1-batchx8/data/intermediate/server/requests \
#     --protocal json_str \
#     --log_scale \
#     --save_dir ./plots/tool_io/swe-ds32B

# python tool_io.py --tag swe-ds32B \
#     --module_dirs /mnt/data/agbench/results/swe/swe-ds32B/stage-1-v0/stage-1-batchx8/data/intermediate/env_manager/requests \
#     /mnt/data/agbench/results/swe/swe-ds32B/stage-1-v0/stage-1-batchx8/data/intermediate/server/requests \
#     --protocal bytes \
#     --log_scale \
#     --save_dir ./plots/tool_io/swe-ds32B

# WebArena Tool IO Size Analysis Script
python tool_io.py --tag webarena \
    --module_dirs /mnt/data/agbench/results/webarena/intermediate/execution_coordinator/requests \
    /mnt/data/agbench/results/webarena/intermediate/env_manager/requests \
    --protocal json_str \
    --log_scale \
    --save_dir ./plots/tool_io/webarena

# python tool_io.py --tag webarena \
#     --module_dirs /mnt/data/agbench/results/webarena/intermediate/execution_coordinator/requests \
#     /mnt/data/agbench/results/webarena/intermediate/env_manager/requests \
#     --protocal bytes \
#     --log_scale \
#     --save_dir ./plots/tool_io/webarena


# MoA Tool IO Size Analysis Script
python tool_io.py --tag moa \
    --module_dirs /mnt/data/agbench/results/moa/intermediate/requests \
    --protocal json_str \
    --log_scale \
    --save_dir ./plots/tool_io/moa

# python tool_io.py --tag moa \
#     --module_dirs /mnt/data/agbench/results/moa/intermediate/requests \
#     --protocal bytes \
#     --log_scale \
#     --save_dir ./plots/tool_io/moa

# Niave RAG Tool IO Size Analysis Script
python tool_io.py --tag rag-naive \
    --module_dirs /mnt/data/agbench/results/rag-v0/naive_rag/stage-1/naive-WQA-OneByOne-0.0-10000/results/server/internal/intermediate/requests \
    --protocal json_str \
    --include_tasks load_docs split_docs embed_documents vdb_store embed_query vdb_search reranking \
    --log_scale \
    --save_dir ./plots/tool_io/naive_rag

# python tool_io.py --tag rag-naive \
#     --module_dirs /mnt/data/agbench/results/rag-v0/naive_rag/stage-1/naive-WQA-OneByOne-0.0-10000/results/server/internal/intermediate/requests \
#     --protocal bytes \
#     --include_tasks load_docs split_docs embed_documents vdb_store embed_query vdb_search reranking \
#     --log_scale \
#     --save_dir ./plots/tool_io/naive_rag

# Advanced RAG Tool IO Size Analysis Script
python tool_io.py --tag rag-advanced \
    --module_dirs /mnt/data/agbench/results/rag-v0/advanced_rag/stage-1/advanced-WQA-OneByOne-0.0-10000/results/server/internal/intermediate/requests \
    --protocal json_str \
    --include_tasks load_docs split_docs embed_documents vdb_store embed_query vdb_search reranking \
    --log_scale \
    --save_dir ./plots/tool_io/advanced_rag

# python tool_io.py --tag rag-advanced \
#     --module_dirs /mnt/data/agbench/results/rag-v0/advanced_rag/stage-1/advanced-WQA-OneByOne-0.0-10000/results/server/internal/intermediate/requests \
#     --protocal bytes \
#     --include_tasks load_docs split_docs embed_documents vdb_store embed_query vdb_search reranking \
#     --log_scale \
#     --save_dir ./plots/tool_io/advanced_rag

# Dynamic RAG Tool IO Size Analysis Script
python tool_io.py --tag rag-dynamic \
    --module_dirs /mnt/data/agbench/results/rag-v0/dynamic_rag/stage-1/dynamic-WQA-OneByOne-0.0-10000/results/server/internal/intermediate/requests \
    --protocal json_str \
    --include_tasks load_docs split_docs embed_documents vdb_store embed_query vdb_search reranking \
    --log_scale \
    --save_dir ./plots/tool_io/dynamic_rag

# python tool_io.py --tag rag-dynamic \
#     --module_dirs /mnt/data/agbench/results/rag-v0/dynamic_rag/stage-1/dynamic-WQA-OneByOne-0.0-10000/results/server/internal/intermediate/requests \
#     --protocal bytes \
#     --include_tasks load_docs split_docs embed_documents vdb_store embed_query vdb_search reranking \
#     --log_scale \
#     --save_dir ./plots/tool_io/dynamic_rag