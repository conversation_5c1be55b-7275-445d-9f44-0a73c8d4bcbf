"""
This script is used to plot the resource usage of the agent.
The data is collected from prometheus and saved to a local file.
The plot is generated based on the saved data.

The serving of an agent involves multiple containers, each of which may have different resource usage patterns.
The script provides functions to plot the resource usage for each container, as well as the sum of all containers.

The resource usage includes:
1. GPU memory usage
2. GPU utilization
3. CPU usage
4. Memory usage
5. Network usage
6. Disk usage
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import argparse
import os
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import glob
import requests
import sys
import logging
import json
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)

module_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
module_path = os.path.dirname(module_path)
print(f"Adding module path: {module_path}")
if module_path not in sys.path:
    print(f"Appending {module_path} to sys.path")
    sys.path.append(module_path)

from analysis.resource.prometheus_data import (
    PrometheusClient,
    get_resource_queries,
    collect_prometheus_data,
    load_prometheus_data,
)


# ============================================================================
# DATA PROCESSING UTILITIES
# ============================================================================


def process_metric_data(
    df: pd.DataFrame,
    include_containers: Optional[List[str]] = None,
    exclude_containers: Optional[List[str]] = None,
    aggregate_prefixes: Optional[List[str]] = None,
) -> pd.DataFrame:
    """Apply filtering and aggregation to metric data in a single step.

    This function combines the common pattern of filter_containers()
    followed by aggregate_containers() that appears throughout the codebase.

    Args:
        df: DataFrame with metric data
        include_containers: Container names/patterns to include
        exclude_containers: Container names/patterns to exclude
        aggregate_prefixes: Prefixes to aggregate containers by

    Returns:
        Processed DataFrame with filtering and aggregation applied
    """
    if df.empty:
        return df

    # Apply filtering
    processed_df = filter_containers(df, include_containers, exclude_containers)

    # Apply aggregation
    processed_df = aggregate_containers(processed_df, aggregate_prefixes)

    return processed_df


def get_processed_metrics(
    data: Dict[str, pd.DataFrame],
    metric_names: List[str],
    include_containers: Optional[List[str]] = None,
    exclude_containers: Optional[List[str]] = None,
    aggregate_prefixes: Optional[List[str]] = None,
) -> Dict[str, pd.DataFrame]:
    """Process multiple metrics with consistent filtering and aggregation.

    Args:
        data: Dictionary of metric DataFrames
        metric_names: List of metric names to process
        include_containers: Container names/patterns to include
        exclude_containers: Container names/patterns to exclude
        aggregate_prefixes: Prefixes to aggregate containers by

    Returns:
        Dictionary of processed DataFrames
    """
    processed_data = {}

    for metric_name in metric_names:
        if metric_name in data:
            df = data[metric_name]
            if not df.empty and "name" in df.columns:
                processed_df = process_metric_data(
                    df, include_containers, exclude_containers, aggregate_prefixes
                )
                processed_data[metric_name] = processed_df
            else:
                # For metrics without container names (like GPU metrics)
                processed_data[metric_name] = df

    return processed_data


def format_time_axis(ax, timestamps, is_heatmap=False):
    """Format x-axis timestamps for plots with relative time formatting.

    This function standardizes time axis formatting across all plotting
    functions, displaying elapsed time from the start of monitoring.

    Args:
        ax: Matplotlib axis object
        timestamps: Series or DatetimeIndex of timestamps to format
        is_heatmap: Boolean indicating if this is for a heatmap (different tick handling)
    """
    if len(timestamps) == 0:
        return

    # Convert to datetime if needed and handle both Series and DatetimeIndex
    if hasattr(timestamps, "iloc"):
        # It's a Series
        if not isinstance(timestamps.iloc[0], pd.Timestamp):
            timestamps = pd.to_datetime(timestamps)
    else:
        # It's likely a DatetimeIndex or similar
        timestamps = pd.to_datetime(timestamps)

    # Calculate total duration and start time
    start_time = timestamps.min()
    duration = timestamps.max() - start_time
    total_seconds = duration.total_seconds()

    # Create formatting function based on duration
    def format_func(elapsed):
        if total_seconds <= 300:  # <= 5 minutes - use seconds
            return f"{int(elapsed)}s"
        elif total_seconds <= 1800:  # <= 30 minutes - use seconds/minutes
            if elapsed < 60:
                return f"{int(elapsed)}s"
            else:
                return f"{int(elapsed/60)}m"
        elif total_seconds <= 7200:  # <= 2 hours - use minutes
            return f"{int(elapsed/60)}m"
        else:  # > 2 hours - use hours and minutes
            if elapsed < 3600:
                return f"{int(elapsed/60)}m"
            else:
                hours = int(elapsed / 3600)
                minutes = int((elapsed % 3600) / 60)
                return f"{hours}h{minutes:02d}m"

    if is_heatmap:
        # For heatmaps, work with existing tick positions set by seaborn
        current_ticks = ax.get_xticks()

        # Map tick positions to timestamp indices
        tick_labels = []
        for tick_pos in current_ticks:
            # Convert tick position to timestamp index
            tick_idx = int(round(tick_pos))
            if 0 <= tick_idx < len(timestamps):
                ts = timestamps[tick_idx]
                elapsed = (ts - start_time).total_seconds()
                if elapsed == 0:
                    tick_labels.append("0s")
                else:
                    tick_labels.append(format_func(elapsed))
            else:
                tick_labels.append("")

        # Limit number of labels for readability
        if len(tick_labels) > 8:
            # Show every nth label
            step = len(tick_labels) // 6
            for i in range(len(tick_labels)):
                if i % step != 0 and i != 0 and i != len(tick_labels) - 1:
                    tick_labels[i] = ""

        ax.set_xticklabels(tick_labels, rotation=45, ha="right")
    else:
        # For line plots, use the original approach with custom tick positions
        # Determine appropriate interval based on duration
        if total_seconds <= 300:  # <= 5 minutes
            interval_seconds = 30
        elif total_seconds <= 1800:  # <= 30 minutes
            interval_seconds = 60
        elif total_seconds <= 7200:  # <= 2 hours
            interval_seconds = 300  # 5 minute intervals
        else:  # > 2 hours
            interval_seconds = 600  # 10 minute intervals

        # Select tick positions based on elapsed time
        tick_times = []
        tick_positions = []

        # Always include the start (0 time)
        # tick_times.append(start_time)
        # tick_positions.append(0)

        # Add intermediate ticks
        for ts in timestamps:
            elapsed = (ts - start_time).total_seconds()
            if elapsed > 0 and (
                len(tick_positions) == 1
                or elapsed - tick_positions[-1] >= interval_seconds
            ):
                tick_times.append(ts)
                tick_positions.append(elapsed)

        # Ensure we don't have too many ticks
        if len(tick_times) > 8:
            step = len(tick_times) // 6
            tick_times = tick_times[::step]
            tick_positions = tick_positions[::step]

        # Format labels with relative time
        tick_labels = []
        for elapsed in tick_positions:
            if elapsed == 0:
                tick_labels.append("0s")
            else:
                tick_labels.append(format_func(elapsed))

        ax.set_xticks(tick_times)
        ax.set_xticklabels(tick_labels, rotation=45, ha="right")

    ax.set_xlabel("Elapsed Time")


# ============================================================================
# STATISTICS COMPUTATION SYSTEM
# ============================================================================


@dataclass
class MetricStatistics:
    """Statistics for a single metric type."""

    total_containers: int
    total_data_points: int
    average_value: float
    peak_value: float
    total_consumption_avg: float
    peak_total_consumption: float
    variance: float
    top_consumers: Dict[str, Dict[str, float]]  # {container: {avg, peak}}
    top_consumer_groups: Dict[str, Dict[str, float]]  # {group: {avg, peak}}
    time_range_hours: float
    start_time: str
    end_time: str
    peak_hour: Optional[str] = None
    peak_hour_value: Optional[float] = None


@dataclass
class ResourceStatistics:
    """Complete resource usage statistics."""

    tag: str
    generation_time: str
    time_range_hours: float
    start_time: str
    end_time: str
    filtering_info: Dict[str, Any]

    # Metric-specific statistics
    cpu: Optional[MetricStatistics] = None
    memory: Optional[MetricStatistics] = None
    network_rx: Optional[MetricStatistics] = None
    network_tx: Optional[MetricStatistics] = None
    filesystem: Optional[MetricStatistics] = None
    gpu_utilization: Optional[MetricStatistics] = None
    gpu_memory: Optional[MetricStatistics] = None

    # Cross-metric efficiency metrics
    efficiency_metrics: Dict[str, str] = None


def compute_metric_statistics(
    df: pd.DataFrame,
    metric_name: str,
    unit_conversion: float = 1.0,
    aggregated_df: Optional[pd.DataFrame] = None,
) -> Optional[MetricStatistics]:
    """Compute comprehensive statistics for a single metric.

    Args:
        df: Processed DataFrame for the metric
        metric_name: Name of the metric for context
        unit_conversion: Factor to convert values to desired units
        aggregated_df: Optional aggregated DataFrame for group analysis

    Returns:
        MetricStatistics object or None if insufficient data
    """
    if df.empty or "name" not in df.columns:
        return None

    # Basic statistics
    total_containers = df["name"].nunique()
    total_data_points = len(df)
    average_value = df["value"].mean() * unit_conversion
    peak_value = df["value"].max() * unit_conversion
    variance = df.groupby("name")["value"].mean().std() * unit_conversion

    # Time-based aggregations
    total_consumption_avg = (
        df.groupby("timestamp")["value"].sum().mean() * unit_conversion
    )
    peak_total_consumption = (
        df.groupby("timestamp")["value"].sum().max() * unit_conversion
    )

    # Time range information
    if "timestamp" in df.columns:
        timestamps = pd.to_datetime(df["timestamp"])
        time_range = timestamps.max() - timestamps.min()
        time_range_hours = time_range.total_seconds() / 3600
        start_time = timestamps.min().strftime("%Y-%m-%d %H:%M:%S")
        end_time = timestamps.max().strftime("%Y-%m-%d %H:%M:%S")

        # Peak hour analysis
        hourly_consumption = (
            df.set_index("timestamp").groupby(pd.Grouper(freq="H"))["value"].sum()
        )
        if len(hourly_consumption) > 1:
            peak_hour_idx = hourly_consumption.idxmax()
            peak_hour = peak_hour_idx.strftime("%H:%M")
            peak_hour_value = hourly_consumption.max() * unit_conversion
        else:
            peak_hour = None
            peak_hour_value = None
    else:
        time_range_hours = 0
        start_time = ""
        end_time = ""
        peak_hour = None
        peak_hour_value = None

    # Top consumers analysis
    top_consumers = {}
    top_consumers_data = (
        df.groupby("name")["value"]
        .agg(["mean", "max"])
        .sort_values("mean", ascending=False)
        .head(5)
    )

    for container, stats in top_consumers_data.iterrows():
        top_consumers[container] = {
            "avg": stats["mean"] * unit_conversion,
            "peak": stats["max"] * unit_conversion,
        }

    # Group analysis
    top_consumer_groups = {}
    if (
        aggregated_df is not None
        and not aggregated_df.empty
        and "name" in aggregated_df.columns
        and aggregated_df["name"].nunique() < df["name"].nunique()
    ):

        top_groups_data = (
            aggregated_df.groupby("name")["value"]
            .agg(["mean", "max"])
            .sort_values("mean", ascending=False)
            .head(5)
        )

        for group, stats in top_groups_data.iterrows():
            top_consumer_groups[group] = {
                "avg": stats["mean"] * unit_conversion,
                "peak": stats["max"] * unit_conversion,
            }

    return MetricStatistics(
        total_containers=total_containers,
        total_data_points=total_data_points,
        average_value=average_value,
        peak_value=peak_value,
        total_consumption_avg=total_consumption_avg,
        peak_total_consumption=peak_total_consumption,
        variance=variance,
        top_consumers=top_consumers,
        top_consumer_groups=top_consumer_groups,
        time_range_hours=time_range_hours,
        start_time=start_time,
        end_time=end_time,
        peak_hour=peak_hour,
        peak_hour_value=peak_hour_value,
    )


def compute_resource_statistics(
    data: Dict[str, pd.DataFrame],
    tag: str,
    include_containers: Optional[List[str]] = None,
    exclude_containers: Optional[List[str]] = None,
    aggregate_prefixes: Optional[List[str]] = None,
) -> ResourceStatistics:
    """Compute comprehensive resource statistics for all metrics.

    Args:
        data: Dictionary of metric DataFrames
        tag: Tag for the experiment/agent
        include_containers: Container names/patterns to include
        exclude_containers: Container names/patterns to exclude
        aggregate_prefixes: Prefixes to aggregate containers by

    Returns:
        ResourceStatistics object with all computed statistics
    """
    generation_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # Process all container-based metrics
    container_metrics = [
        "container_cpu_usage",
        "container_memory_usage",
        "container_network_rx",
        "container_network_tx",
        "container_fs_usage",
    ]

    processed_data = get_processed_metrics(
        data,
        container_metrics,
        include_containers,
        exclude_containers,
        aggregate_prefixes,
    )

    # Process aggregated data for group analysis
    aggregated_data = {}
    if aggregate_prefixes:
        for metric_name in container_metrics:
            if metric_name in data:
                df = data[metric_name]
                if not df.empty and "name" in df.columns:
                    filtered_df = filter_containers(
                        df, include_containers, exclude_containers
                    )
                    agg_df = aggregate_containers(filtered_df, aggregate_prefixes)
                    aggregated_data[metric_name] = agg_df

    # Get time range from any available metric
    time_range_hours = 0
    start_time = ""
    end_time = ""
    if processed_data:
        sample_df = next(iter(processed_data.values()))
        if not sample_df.empty and "timestamp" in sample_df.columns:
            timestamps = pd.to_datetime(sample_df["timestamp"])
            time_range = timestamps.max() - timestamps.min()
            time_range_hours = time_range.total_seconds() / 3600
            start_time = timestamps.min().strftime("%Y-%m-%d %H:%M:%S")
            end_time = timestamps.max().strftime("%Y-%m-%d %H:%M:%S")

    # Filtering information
    filtering_info = {
        "include_containers": include_containers,
        "exclude_containers": exclude_containers,
        "aggregate_prefixes": aggregate_prefixes,
    }

    # Compute statistics for each metric type
    cpu_stats = None
    if "container_cpu_usage" in processed_data:
        cpu_stats = compute_metric_statistics(
            processed_data["container_cpu_usage"],
            "cpu",
            unit_conversion=1.0,  # cores
            aggregated_df=aggregated_data.get("container_cpu_usage"),
        )

    memory_stats = None
    if "container_memory_usage" in processed_data:
        memory_stats = compute_metric_statistics(
            processed_data["container_memory_usage"],
            "memory",
            unit_conversion=1.0 / (1024**3),  # Convert to GB
            aggregated_df=aggregated_data.get("container_memory_usage"),
        )

    network_rx_stats = None
    if "container_network_rx" in processed_data:
        network_rx_stats = compute_metric_statistics(
            processed_data["container_network_rx"],
            "network_rx",
            unit_conversion=1.0 / (1024**2),  # Convert to MB/s
            aggregated_df=aggregated_data.get("container_network_rx"),
        )

    network_tx_stats = None
    if "container_network_tx" in processed_data:
        network_tx_stats = compute_metric_statistics(
            processed_data["container_network_tx"],
            "network_tx",
            unit_conversion=1.0 / (1024**2),  # Convert to MB/s
            aggregated_df=aggregated_data.get("container_network_tx"),
        )

    filesystem_stats = None
    if "container_fs_usage" in processed_data:
        filesystem_stats = compute_metric_statistics(
            processed_data["container_fs_usage"],
            "filesystem",
            unit_conversion=1.0 / (1024**3),  # Convert to GB
            aggregated_df=aggregated_data.get("container_fs_usage"),
        )

    # GPU statistics (no container filtering needed)
    gpu_utilization_stats = None
    if "gpu_utilization" in data and not data["gpu_utilization"].empty:
        gpu_utilization_stats = compute_metric_statistics(
            data["gpu_utilization"], "gpu_utilization", unit_conversion=1.0
        )

    gpu_memory_stats = None
    if "gpu_memory_used" in data and not data["gpu_memory_used"].empty:
        gpu_memory_stats = compute_metric_statistics(
            data["gpu_memory_used"], "gpu_memory", unit_conversion=1.0
        )

    # Compute efficiency metrics
    efficiency_metrics = {}

    if cpu_stats:
        cpu_balance = "Good" if cpu_stats.variance < 0.5 else "Poor"
        efficiency_metrics["cpu_load_balancing"] = (
            f"{cpu_balance} (variance: {cpu_stats.variance:.3f})"
        )

    if memory_stats and "container_memory_limit" in data:
        limit_df = data["container_memory_limit"]
        if not limit_df.empty and "name" in limit_df.columns:
            # Calculate memory utilization efficiency
            mem_df = processed_data["container_memory_usage"]
            usage_by_container = mem_df.groupby("name")["value"].mean()
            limits_by_container = limit_df.groupby("name")["value"].mean()
            utilization_ratios = (
                usage_by_container / limits_by_container * 100
            ).dropna()
            if not utilization_ratios.empty:
                avg_utilization = utilization_ratios.mean()
                mem_efficiency = "Good" if avg_utilization > 60 else "Poor"
                efficiency_metrics["memory_efficiency"] = (
                    f"{mem_efficiency} ({avg_utilization:.1f}% avg utilization)"
                )

    return ResourceStatistics(
        tag=tag,
        generation_time=generation_time,
        time_range_hours=time_range_hours,
        start_time=start_time,
        end_time=end_time,
        filtering_info=filtering_info,
        cpu=cpu_stats,
        memory=memory_stats,
        network_rx=network_rx_stats,
        network_tx=network_tx_stats,
        filesystem=filesystem_stats,
        gpu_utilization=gpu_utilization_stats,
        gpu_memory=gpu_memory_stats,
        efficiency_metrics=efficiency_metrics,
    )


def save_resource_statistics(stats: ResourceStatistics, save_dir: str, tag: str) -> str:
    """Save resource statistics to JSON file.

    Args:
        stats: ResourceStatistics object to save
        save_dir: Directory to save the JSON file
        tag: Tag for the experiment/agent

    Returns:
        Path to the saved JSON file
    """
    os.makedirs(save_dir, exist_ok=True)
    stats_path = os.path.join(save_dir, f"{tag}_resource_statistics.json")

    # Convert to dictionary for JSON serialization
    stats_dict = asdict(stats)

    with open(stats_path, "w") as f:
        json.dump(stats_dict, f, indent=2, default=str)

    return stats_path


def load_resource_statistics(save_dir: str, tag: str) -> Optional[ResourceStatistics]:
    """Load resource statistics from JSON file.

    Args:
        save_dir: Directory containing the JSON file
        tag: Tag for the experiment/agent

    Returns:
        ResourceStatistics object or None if file doesn't exist
    """
    stats_path = os.path.join(save_dir, f"{tag}_resource_statistics.json")

    if not os.path.exists(stats_path):
        return None

    try:
        with open(stats_path, "r") as f:
            stats_dict = json.load(f)

        # Convert back to ResourceStatistics object
        # Note: This is a simplified conversion - in practice you might want
        # more robust deserialization
        return ResourceStatistics(**stats_dict)
    except Exception as e:
        logger.warning(f"Failed to load statistics from {stats_path}: {e}")
        return None


def filter_containers(
    df: pd.DataFrame,
    include_containers: Optional[List[str]] = None,
    exclude_containers: Optional[List[str]] = None,
) -> pd.DataFrame:
    """Filter containers based on include/exclude lists"""
    if df.empty:
        return df

    # Apply include filter
    if include_containers:
        mask = df["name"].str.contains(
            "|".join(include_containers), case=False, na=False
        )
        df = df[mask]

    # Apply exclude filter
    if exclude_containers:
        mask = ~df["name"].str.contains(
            "|".join(exclude_containers), case=False, na=False
        )
        df = df[mask]

    return df


def shorten_container_names(containers: List[str]) -> Dict[str, str]:
    """Create a mapping of original container names to shortened names

    Intelligently extracts the most meaningful parts of container names by:
    1. Identifying service/component names
    2. Handling complex naming patterns
    3. Prioritizing meaningful keywords
    4. Ensuring uniqueness while maintaining readability

    Args:
        containers: List of container names to shorten

    Returns:
        Dictionary mapping original container names to shortened versions
    """
    if not containers:
        return {}

    # Common service keywords to prioritize
    service_keywords = [
        "server",
        "client",
        "agent",
        "service",
        "loadgen",
        "worker",
        "database",
        "cache",
        "proxy",
        "api",
        "frontend",
        "backend",
        "embedding",
        "llm",
        "vllm",
        "reranking",
        "situating",
    ]

    # Common prefixes that can be removed
    common_prefixes = ["k8s_", "docker_", "container_", "pod_"]

    # Initialize mapping
    name_map = {}

    # First pass: Extract meaningful components
    for name in containers:
        # Remove registry paths if present
        if "/" in name:
            name_without_registry = name.split("/")[-1]
        else:
            name_without_registry = name

        # Remove image tags if present
        if ":" in name_without_registry:
            parts = name_without_registry.split(":")
            name_without_tag = parts[0]
            # Keep version info if it's the only distinguishing factor
            version = (
                parts[1].split("-")[0]
                if len(parts) > 1 and len(parts[1].split("-")) > 0
                else ""
            )
        else:
            name_without_tag = name_without_registry
            version = ""

        # Remove common prefixes
        for prefix in common_prefixes:
            if name_without_tag.startswith(prefix):
                name_without_tag = name_without_tag[len(prefix) :]
                break

        # Split by common separators
        components = []
        for part in name_without_tag.replace("_", "-").split("-"):
            if part and not part.isdigit():  # Skip empty parts and pure numbers
                components.append(part)

        # Look for service keywords in components
        found_keywords = [
            comp for comp in components if comp.lower() in service_keywords
        ]

        # Determine the best short name
        if found_keywords:
            # Prioritize service keywords
            short_name = found_keywords[0]
            # Add version if available and short name is generic
            if version and len(short_name) < 8:
                short_name = f"{short_name}-{version}"
        elif components:
            # Use the most specific component (usually the last non-numeric one)
            candidates = [comp for comp in reversed(components) if not comp.isdigit()]
            if candidates:
                short_name = candidates[0]
                # If too short, add another component for context
                if len(short_name) < 4 and len(components) > 1:
                    idx = components.index(short_name)
                    if idx > 0:
                        short_name = f"{components[idx-1]}-{short_name}"
            else:
                # Fallback to first component
                short_name = components[0] if components else "container"
        else:
            # Last resort: use a substring of the original name
            short_name = name_without_tag[:15]

        # Store the mapping
        name_map[name] = short_name

    # Second pass: Ensure uniqueness
    unique_short_names = {}
    duplicates = {}

    # Find duplicates
    for orig, short in name_map.items():
        if short in unique_short_names:
            if short not in duplicates:
                duplicates[short] = [unique_short_names[short]]
            duplicates[short].append(orig)
        else:
            unique_short_names[short] = orig

    # Resolve duplicates
    for short_name, dup_containers in duplicates.items():
        # For each duplicate, try to find a more specific name
        for i, container in enumerate(dup_containers):
            components = container.replace("_", "-").split("-")

            # Try to find distinguishing components
            if len(components) > 1:
                # Look for numbers or unique identifiers at the end
                suffix = next(
                    (
                        comp
                        for comp in reversed(components)
                        if comp.isdigit() or len(comp) <= 3
                    ),
                    "",
                )
                if suffix:
                    name_map[container] = f"{short_name}-{suffix}"
                else:
                    # Add a numeric suffix as last resort
                    name_map[container] = f"{short_name}-{i+1}"
            else:
                # Just add a number
                name_map[container] = f"{short_name}-{i+1}"

    # Third pass: Check for uniqueness again and ensure readability
    final_map = {}
    used_names = set()

    for orig, short in name_map.items():
        # Ensure uniqueness
        if short in used_names:
            # Add a hash suffix
            import hashlib

            hash_suffix = hashlib.md5(orig.encode()).hexdigest()[:4]
            short = f"{short}-{hash_suffix}"

        # Ensure readability (max 20 chars)
        if len(short) > 20:
            short = f"{short[:8]}..{short[-8:]}"

        final_map[orig] = short
        used_names.add(short)

    return final_map


def aggregate_containers(
    df: pd.DataFrame, aggregate_prefixes: Optional[List[str]] = None
) -> pd.DataFrame:
    """Aggregate containers by specified prefixes"""
    if df.empty or not aggregate_prefixes:
        return df

    # Create aggregation groups
    df_copy = df.copy()
    df_copy["group_name"] = df_copy["name"]

    for prefix in aggregate_prefixes:
        mask = df_copy["name"].str.startswith(prefix)
        df_copy.loc[mask, "group_name"] = f"{prefix}_group"

    # Aggregate by group and timestamp
    agg_df = (
        df_copy.groupby(["group_name", "timestamp"])
        .agg({"value": "sum", "metric": "first"})
        .reset_index()
    )

    # Rename group_name back to name
    agg_df = agg_df.rename(columns={"group_name": "name"})

    return agg_df


def get_top_containers(
    df: pd.DataFrame, max_containers: int = 10, metric: str = "mean"
) -> List[str]:
    """Get top containers by resource usage"""
    if df.empty:
        return []

    if metric == "mean":
        top_containers = df.groupby("name")["value"].mean().sort_values(ascending=False)
    elif metric == "max":
        top_containers = df.groupby("name")["value"].max().sort_values(ascending=False)
    else:
        top_containers = df.groupby("name")["value"].sum().sort_values(ascending=False)

    return top_containers.head(max_containers).index.tolist()


def plot_gpu_usage(
    data: Dict[str, pd.DataFrame], tag: str, save_dir: str = "./plots"
) -> str:
    """Plot GPU memory usage and utilization"""
    os.makedirs(save_dir, exist_ok=True)

    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle(f"GPU Usage - {tag}", fontsize=16)

    # GPU Memory Usage
    if "gpu_memory_used" in data:
        df = data["gpu_memory_used"]
        for gpu_id in df["gpu"].unique():
            gpu_data = df[df["gpu"] == gpu_id]
            gpu_data["timestamp"] = pd.to_datetime(gpu_data["timestamp"])
            axes[0, 0].plot(
                gpu_data["timestamp"],
                gpu_data["value"],
                label=f"GPU {gpu_id}",
                alpha=0.7,
            )
        axes[0, 0].set_title("GPU Memory Utilization (%)")
        axes[0, 0].set_ylabel("Utilization %")
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # Format time axis
        all_timestamps = pd.to_datetime(df["timestamp"])
        format_time_axis(axes[0, 0], all_timestamps)

    # GPU Utilization
    if "gpu_utilization" in data:
        df = data["gpu_utilization"]
        for gpu_id in df["gpu"].unique():
            gpu_data = df[df["gpu"] == gpu_id]
            gpu_data["timestamp"] = pd.to_datetime(gpu_data["timestamp"])
            axes[0, 1].plot(
                gpu_data["timestamp"],
                gpu_data["value"],
                label=f"GPU {gpu_id}",
                alpha=0.7,
            )
        axes[0, 1].set_title("GPU Compute Utilization (%)")
        axes[0, 1].set_ylabel("Utilization %")
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)

        # Format time axis
        all_timestamps = pd.to_datetime(df["timestamp"])
        format_time_axis(axes[0, 1], all_timestamps)

    # GPU Memory Free
    if "gpu_memory_free" in data:
        df = data["gpu_memory_free"]
        for gpu_id in df["gpu"].unique():
            gpu_data = df[df["gpu"] == gpu_id]
            gpu_data["timestamp"] = pd.to_datetime(gpu_data["timestamp"])
            axes[1, 0].plot(
                gpu_data["timestamp"],
                gpu_data["value"] / 1024**3,
                label=f"GPU {gpu_id}",
                alpha=0.7,
            )
        axes[1, 0].set_title("GPU Memory Free (GB)")
        axes[1, 0].set_ylabel("Memory (GB)")
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

        # Format time axis
        all_timestamps = pd.to_datetime(df["timestamp"])
        format_time_axis(axes[1, 0], all_timestamps)

    # GPU Memory Total (if available)
    if "gpu_memory_total" in data:
        df = data["gpu_memory_total"]
        for gpu_id in df["gpu"].unique():
            gpu_data = df[df["gpu"] == gpu_id]
            gpu_data["timestamp"] = pd.to_datetime(gpu_data["timestamp"])
            axes[1, 1].plot(
                gpu_data["timestamp"],
                gpu_data["value"] / 1024**3,
                label=f"GPU {gpu_id}",
                alpha=0.7,
            )
        axes[1, 1].set_title("GPU Memory Total (GB)")
        axes[1, 1].set_ylabel("Memory (GB)")
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)

        # Format time axis
        all_timestamps = pd.to_datetime(df["timestamp"])
        format_time_axis(axes[1, 1], all_timestamps)
    else:
        axes[1, 1].text(
            0.5,
            0.5,
            "GPU Memory Total\nData Not Available",
            ha="center",
            va="center",
            transform=axes[1, 1].transAxes,
        )

    plt.tight_layout()
    fig_path = os.path.join(save_dir, f"{tag}_gpu_usage.png")
    plt.savefig(fig_path, dpi=300, bbox_inches="tight")
    plt.close()

    return fig_path


def plot_container_resources(
    data: Dict[str, pd.DataFrame],
    tag: str,
    save_dir: str = "./plots",
    include_containers: Optional[List[str]] = None,
    exclude_containers: Optional[List[str]] = None,
    max_containers: int = 10,
    aggregate_prefixes: Optional[List[str]] = None,
) -> str:
    """Plot container-level CPU, memory, and network usage"""
    os.makedirs(save_dir, exist_ok=True)

    fig, axes = plt.subplots(2, 3, figsize=(20, 12))
    fig.suptitle(f"Container Resource Usage - {tag}", fontsize=16)

    # Container CPU Usage
    if "container_cpu_usage" in data:
        df = process_metric_data(
            data["container_cpu_usage"],
            include_containers,
            exclude_containers,
            aggregate_prefixes,
        )

        if not df.empty:
            containers = get_top_containers(df, max_containers)
            name_map = shorten_container_names(containers)

            for container in containers:
                container_data = df[df["name"] == container]
                container_data["timestamp"] = pd.to_datetime(
                    container_data["timestamp"]
                )
                short_name = name_map.get(container, container)
                axes[0, 0].plot(
                    container_data["timestamp"],
                    container_data["value"],
                    label=short_name,
                    alpha=0.7,
                )

            axes[0, 0].set_title("Container CPU Usage (cores)")
            axes[0, 0].set_ylabel("CPU Cores")
            axes[0, 0].legend(bbox_to_anchor=(1.05, 1), loc="upper left")
            axes[0, 0].grid(True, alpha=0.3)

            # Format time axis
            all_timestamps = pd.concat(
                [df[df["name"] == c]["timestamp"] for c in containers]
            )
            format_time_axis(axes[0, 0], all_timestamps)

    # Container Memory Usage
    if "container_memory_usage" in data:
        df = process_metric_data(
            data["container_memory_usage"],
            include_containers,
            exclude_containers,
            aggregate_prefixes,
        )

        if not df.empty:
            containers = get_top_containers(df, max_containers)
            name_map = shorten_container_names(containers)

            for container in containers:
                container_data = df[df["name"] == container]
                container_data["timestamp"] = pd.to_datetime(
                    container_data["timestamp"]
                )
                short_name = name_map.get(container, container)
                axes[0, 1].plot(
                    container_data["timestamp"],
                    container_data["value"] / 1024**3,
                    label=short_name,
                    alpha=0.7,
                )

            axes[0, 1].set_title("Container Memory Usage (GB)")
            axes[0, 1].set_ylabel("Memory (GB)")
            axes[0, 1].legend(bbox_to_anchor=(1.05, 1), loc="upper left")
            axes[0, 1].grid(True, alpha=0.3)

            # Format time axis
            all_timestamps = pd.concat(
                [df[df["name"] == c]["timestamp"] for c in containers]
            )
            format_time_axis(axes[0, 1], all_timestamps)

    # Container Network RX
    if "container_network_rx" in data:
        df = process_metric_data(
            data["container_network_rx"],
            include_containers,
            exclude_containers,
            aggregate_prefixes,
        )

        if not df.empty:
            containers = get_top_containers(df, max_containers)
            name_map = shorten_container_names(containers)

            for container in containers:
                container_data = df[df["name"] == container]
                container_data["timestamp"] = pd.to_datetime(
                    container_data["timestamp"]
                )
                short_name = name_map.get(container, container)
                axes[0, 2].plot(
                    container_data["timestamp"],
                    container_data["value"] / 1024**2,
                    label=short_name,
                    alpha=0.7,
                )

            axes[0, 2].set_title("Container Network RX (MB/s)")
            axes[0, 2].set_ylabel("Network RX (MB/s)")
            axes[0, 2].legend(bbox_to_anchor=(1.05, 1), loc="upper left")
            axes[0, 2].grid(True, alpha=0.3)

            # Format time axis
            all_timestamps = pd.concat(
                [df[df["name"] == c]["timestamp"] for c in containers]
            )
            format_time_axis(axes[0, 2], all_timestamps)

    # Container Network TX
    if "container_network_tx" in data:
        df = process_metric_data(
            data["container_network_tx"],
            include_containers,
            exclude_containers,
            aggregate_prefixes,
        )

        if not df.empty:
            containers = get_top_containers(df, max_containers)
            name_map = shorten_container_names(containers)

            for container in containers:
                container_data = df[df["name"] == container]
                container_data["timestamp"] = pd.to_datetime(
                    container_data["timestamp"]
                )
                short_name = name_map.get(container, container)
                axes[1, 0].plot(
                    container_data["timestamp"],
                    container_data["value"] / 1024**2,
                    label=short_name,
                    alpha=0.7,
                )

            axes[1, 0].set_title("Container Network TX (MB/s)")
            axes[1, 0].set_ylabel("Network TX (MB/s)")
            axes[1, 0].legend(bbox_to_anchor=(1.05, 1), loc="upper left")
            axes[1, 0].grid(True, alpha=0.3)

            # Format time axis
            all_timestamps = pd.concat(
                [df[df["name"] == c]["timestamp"] for c in containers]
            )
            format_time_axis(axes[1, 0], all_timestamps)

    # Container Filesystem Usage
    if "container_fs_usage" in data:
        df = process_metric_data(
            data["container_fs_usage"],
            include_containers,
            exclude_containers,
            aggregate_prefixes,
        )

        if not df.empty:
            containers = get_top_containers(df, max_containers)
            name_map = shorten_container_names(containers)

            for container in containers:
                container_data = df[df["name"] == container]
                container_data["timestamp"] = pd.to_datetime(
                    container_data["timestamp"]
                )
                short_name = name_map.get(container, container)
                axes[1, 1].plot(
                    container_data["timestamp"],
                    container_data["value"] / 1024**3,
                    label=short_name,
                    alpha=0.7,
                )

            axes[1, 1].set_title("Container Filesystem Usage (GB)")
            axes[1, 1].set_ylabel("Disk Usage (GB)")
            axes[1, 1].legend(bbox_to_anchor=(1.05, 1), loc="upper left")
            axes[1, 1].grid(True, alpha=0.3)

            # Format time axis
            all_timestamps = pd.concat(
                [df[df["name"] == c]["timestamp"] for c in containers]
            )
            format_time_axis(axes[1, 1], all_timestamps)

    # Container Memory Limits
    if "container_memory_limit" in data:
        df = process_metric_data(
            data["container_memory_limit"],
            include_containers,
            exclude_containers,
            aggregate_prefixes,
        )

        if not df.empty:
            containers = get_top_containers(df, max_containers)
            name_map = shorten_container_names(containers)

            for container in containers:
                container_data = df[df["name"] == container]
                container_data["timestamp"] = pd.to_datetime(
                    container_data["timestamp"]
                )
                short_name = name_map.get(container, container)
                axes[1, 2].plot(
                    container_data["timestamp"],
                    container_data["value"] / 1024**3,
                    label=short_name,
                    alpha=0.7,
                )

            axes[1, 2].set_title("Container Memory Limits (GB)")
            axes[1, 2].set_ylabel("Memory Limit (GB)")
            axes[1, 2].legend(bbox_to_anchor=(1.05, 1), loc="upper left")
            axes[1, 2].grid(True, alpha=0.3)

            # Format time axis
            all_timestamps = pd.concat(
                [df[df["name"] == c]["timestamp"] for c in containers]
            )
            format_time_axis(axes[1, 2], all_timestamps)

    plt.tight_layout()
    fig_path = os.path.join(save_dir, f"{tag}_container_resources.png")
    plt.savefig(fig_path, dpi=300, bbox_inches="tight")
    plt.close()

    return fig_path


def plot_resource_heatmap(
    data: Dict[str, pd.DataFrame],
    tag: str,
    save_dir: str = "./plots",
    include_containers: Optional[List[str]] = None,
    exclude_containers: Optional[List[str]] = None,
    max_containers: int = 15,
    aggregate_prefixes: Optional[List[str]] = None,
) -> str:
    """Plot resource usage heatmap across containers and time"""
    import matplotlib.dates as mdates

    os.makedirs(save_dir, exist_ok=True)

    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle(f"Resource Usage Heatmap - {tag}", fontsize=16)

    # CPU Usage Heatmap
    if "container_cpu_usage" in data:
        df = process_metric_data(
            data["container_cpu_usage"],
            include_containers,
            exclude_containers,
            aggregate_prefixes,
        )

        if not df.empty:
            containers = get_top_containers(df, max_containers)
            df_filtered = df[df["name"].isin(containers)].copy()
            name_map = shorten_container_names(containers)
            df_filtered["short_name"] = df_filtered["name"].map(name_map)

            pivot_data = df_filtered.pivot_table(
                values="value", index="short_name", columns="timestamp", aggfunc="mean"
            )
            if not pivot_data.empty:
                # Apply standardized time formatting first
                timestamps = pd.to_datetime(pivot_data.columns)

                sns.heatmap(
                    pivot_data,
                    ax=axes[0, 0],
                    cmap="YlOrRd",
                    cbar_kws={"label": "CPU Cores"},
                    xticklabels=True,  # Enable x-axis labels for time formatting
                )
                axes[0, 0].set_title("CPU Usage by Container")
                axes[0, 0].set_xlabel("Elapsed Time")
                axes[0, 0].set_ylabel("Container")

                # Apply time formatting after heatmap creation
                format_time_axis(axes[0, 0], timestamps, is_heatmap=True)

    # Memory Usage Heatmap
    if "container_memory_usage" in data:
        df = process_metric_data(
            data["container_memory_usage"],
            include_containers,
            exclude_containers,
            aggregate_prefixes,
        )

        if not df.empty:
            containers = get_top_containers(df, max_containers)
            df_filtered = df[df["name"].isin(containers)].copy()
            name_map = shorten_container_names(containers)
            df_filtered["short_name"] = df_filtered["name"].map(name_map)
            df_filtered["value_gb"] = df_filtered["value"] / 1024**3

            pivot_data = df_filtered.pivot_table(
                values="value_gb",
                index="short_name",
                columns="timestamp",
                aggfunc="mean",
            )
            if not pivot_data.empty:
                # Apply standardized time formatting first
                timestamps = pd.to_datetime(pivot_data.columns)

                sns.heatmap(
                    pivot_data,
                    ax=axes[0, 1],
                    cmap="Blues",
                    cbar_kws={"label": "Memory (GB)"},
                    xticklabels=True,  # Enable x-axis labels for time formatting
                )
                axes[0, 1].set_title("Memory Usage by Container")
                axes[0, 1].set_xlabel("Elapsed Time")
                axes[0, 1].set_ylabel("Container")

                # Apply time formatting after heatmap creation
                format_time_axis(axes[0, 1], timestamps, is_heatmap=True)

    # Network RX Heatmap
    if "container_network_rx" in data:
        df = process_metric_data(
            data["container_network_rx"],
            include_containers,
            exclude_containers,
            aggregate_prefixes,
        )

        if not df.empty:
            containers = get_top_containers(df, max_containers)
            df_filtered = df[df["name"].isin(containers)].copy()
            name_map = shorten_container_names(containers)
            df_filtered["short_name"] = df_filtered["name"].map(name_map)
            df_filtered["value_mb"] = df_filtered["value"] / 1024**2

            pivot_data = df_filtered.pivot_table(
                values="value_mb",
                index="short_name",
                columns="timestamp",
                aggfunc="mean",
            )
            if not pivot_data.empty:
                # Apply standardized time formatting first
                timestamps = pd.to_datetime(pivot_data.columns)

                sns.heatmap(
                    pivot_data,
                    ax=axes[1, 0],
                    cmap="Greens",
                    cbar_kws={"label": "Network RX (MB/s)"},
                    xticklabels=True,  # Enable x-axis labels for time formatting
                )
                axes[1, 0].set_title("Network RX by Container")
                axes[1, 0].set_xlabel("Elapsed Time")
                axes[1, 0].set_ylabel("Container")

                # Apply time formatting after heatmap creation
                format_time_axis(axes[1, 0], timestamps, is_heatmap=True)

    # Filesystem Usage Heatmap
    if "container_fs_usage" in data:
        df = process_metric_data(
            data["container_fs_usage"],
            include_containers,
            exclude_containers,
            aggregate_prefixes,
        )

        if not df.empty:
            containers = get_top_containers(df, max_containers)
            df_filtered = df[df["name"].isin(containers)].copy()
            name_map = shorten_container_names(containers)
            df_filtered["short_name"] = df_filtered["name"].map(name_map)
            df_filtered["value_gb"] = df_filtered["value"] / 1024**3

            pivot_data = df_filtered.pivot_table(
                values="value_gb",
                index="short_name",
                columns="timestamp",
                aggfunc="mean",
            )
            if not pivot_data.empty:
                # Apply standardized time formatting first
                timestamps = pd.to_datetime(pivot_data.columns)

                sns.heatmap(
                    pivot_data,
                    ax=axes[1, 1],
                    cmap="Purples",
                    cbar_kws={"label": "Disk Usage (GB)"},
                    xticklabels=True,  # Enable x-axis labels for time formatting
                )
                axes[1, 1].set_title("Filesystem Usage by Container")
                axes[1, 1].set_xlabel("Elapsed Time")
                axes[1, 1].set_ylabel("Container")

                # Apply time formatting after heatmap creation
                format_time_axis(axes[1, 1], timestamps, is_heatmap=True)

    plt.tight_layout()
    fig_path = os.path.join(save_dir, f"{tag}_resource_heatmap.png")
    plt.savefig(fig_path, dpi=300, bbox_inches="tight")
    plt.close()

    return fig_path


def plot_resource_summary(
    data: Dict[str, pd.DataFrame],
    tag: str,
    save_dir: str = "./plots",
    include_containers: Optional[List[str]] = None,
    exclude_containers: Optional[List[str]] = None,
    aggregate_prefixes: Optional[List[str]] = None,
) -> str:
    """Plot aggregated resource usage summary"""
    os.makedirs(save_dir, exist_ok=True)

    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle(f"Resource Usage Summary - {tag}", fontsize=16)

    # Total CPU Usage
    if "container_cpu_usage" in data:
        df = process_metric_data(
            data["container_cpu_usage"],
            include_containers,
            exclude_containers,
            aggregate_prefixes,
        )

        if not df.empty:
            total_cpu = df.groupby("timestamp")["value"].sum().reset_index()
            total_cpu["timestamp"] = pd.to_datetime(total_cpu["timestamp"])
            axes[0, 0].plot(
                total_cpu["timestamp"], total_cpu["value"], "b-", linewidth=2
            )
            axes[0, 0].set_title("Total CPU Usage (Filtered Containers)")
            axes[0, 0].set_ylabel("CPU Cores")
            axes[0, 0].grid(True, alpha=0.3)
            format_time_axis(axes[0, 0], total_cpu["timestamp"])

    # Total Memory Usage
    if "container_memory_usage" in data:
        df = process_metric_data(
            data["container_memory_usage"],
            include_containers,
            exclude_containers,
            aggregate_prefixes,
        )

        if not df.empty:
            total_memory = df.groupby("timestamp")["value"].sum().reset_index()
            total_memory["timestamp"] = pd.to_datetime(total_memory["timestamp"])
            total_memory["value_gb"] = total_memory["value"] / 1024**3
            axes[0, 1].plot(
                total_memory["timestamp"], total_memory["value_gb"], "g-", linewidth=2
            )
            axes[0, 1].set_title("Total Memory Usage (Filtered Containers)")
            axes[0, 1].set_ylabel("Memory (GB)")
            axes[0, 1].grid(True, alpha=0.3)
            format_time_axis(axes[0, 1], total_memory["timestamp"])

    # Total Network Usage
    if "container_network_rx" in data and "container_network_tx" in data:
        rx_df = process_metric_data(
            data["container_network_rx"],
            include_containers,
            exclude_containers,
            aggregate_prefixes,
        )
        tx_df = process_metric_data(
            data["container_network_tx"],
            include_containers,
            exclude_containers,
            aggregate_prefixes,
        )

        if not rx_df.empty and not tx_df.empty:
            total_rx = rx_df.groupby("timestamp")["value"].sum().reset_index()
            total_tx = tx_df.groupby("timestamp")["value"].sum().reset_index()
            total_rx["timestamp"] = pd.to_datetime(total_rx["timestamp"])
            total_tx["timestamp"] = pd.to_datetime(total_tx["timestamp"])
            total_rx["value_mb"] = total_rx["value"] / 1024**2
            total_tx["value_mb"] = total_tx["value"] / 1024**2

            axes[1, 0].plot(
                total_rx["timestamp"],
                total_rx["value_mb"],
                "r-",
                linewidth=2,
                label="RX",
            )
            axes[1, 0].plot(
                total_tx["timestamp"],
                total_tx["value_mb"],
                "b-",
                linewidth=2,
                label="TX",
            )
            axes[1, 0].set_title("Total Network Usage (Filtered Containers)")
            axes[1, 0].set_ylabel("Network (MB/s)")
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)
            format_time_axis(axes[1, 0], total_rx["timestamp"])

    # Resource utilization distribution
    if "container_cpu_usage" in data:
        df = process_metric_data(
            data["container_cpu_usage"],
            include_containers,
            exclude_containers,
            aggregate_prefixes,
        )

        if not df.empty:
            axes[1, 1].hist(
                df["value"], bins=50, alpha=0.7, color="skyblue", edgecolor="black"
            )
            axes[1, 1].set_title("CPU Usage Distribution (Filtered Containers)")
            axes[1, 1].set_xlabel("CPU Cores")
            axes[1, 1].set_ylabel("Frequency")
            axes[1, 1].grid(True, alpha=0.3)

    plt.tight_layout()
    fig_path = os.path.join(save_dir, f"{tag}_resource_summary.png")
    plt.savefig(fig_path, dpi=300, bbox_inches="tight")
    plt.close()

    return fig_path


def generate_resource_report(
    data: Dict[str, pd.DataFrame],
    tag: str,
    save_dir: str = "./plots",
    include_containers: Optional[List[str]] = None,
    exclude_containers: Optional[List[str]] = None,
    aggregate_prefixes: Optional[List[str]] = None,
) -> str:
    """Generate a comprehensive resource usage report using structured statistics.

    This function now computes statistics once and saves them to JSON, then
    generates the text report from those structured statistics.
    """
    os.makedirs(save_dir, exist_ok=True)

    # First, compute and save structured statistics
    stats = compute_resource_statistics(
        data, tag, include_containers, exclude_containers, aggregate_prefixes
    )
    stats_path = save_resource_statistics(stats, save_dir, tag)
    logger.info(f"Saved structured statistics to: {stats_path}")

    # Generate text report from structured statistics
    report_path = os.path.join(save_dir, f"{tag}_resource_report.txt")

    with open(report_path, "w") as f:
        _write_report_from_statistics(f, stats)

    return report_path


def _write_report_from_statistics(f, stats: ResourceStatistics):
    """Write the text report using structured statistics."""
    f.write(f"Resource Usage Report - {stats.tag}\n")
    f.write("=" * 60 + "\n")
    f.write(f"Generated at: {stats.generation_time}\n\n")

    # Overview section
    f.write("OVERVIEW\n")
    f.write("-" * 30 + "\n")

    metrics_count = sum(
        1
        for metric in [
            stats.cpu,
            stats.memory,
            stats.network_rx,
            stats.network_tx,
            stats.filesystem,
            stats.gpu_utilization,
            stats.gpu_memory,
        ]
        if metric is not None
    )

    f.write(f"Metrics analyzed: {metrics_count}\n")
    f.write(f"Time range: {stats.time_range_hours:.1f} hours\n")
    f.write(f"Start time: {stats.start_time}\n")
    f.write(f"End time: {stats.end_time}\n")

    # Filtering information
    filtering = stats.filtering_info
    if any(filtering.values()):
        f.write(f"\nFiltering applied:\n")
        if filtering.get("include_containers"):
            containers = ", ".join(filtering["include_containers"])
            f.write(f"  Include containers: {containers}\n")
        if filtering.get("exclude_containers"):
            containers = ", ".join(filtering["exclude_containers"])
            f.write(f"  Exclude containers: {containers}\n")
        if filtering.get("aggregate_prefixes"):
            prefixes = ", ".join(filtering["aggregate_prefixes"])
            f.write(f"  Aggregate prefixes: {prefixes}\n")
    f.write("\n")

    # CPU Analysis
    if stats.cpu:
        _write_metric_analysis(f, "CPU", stats.cpu, "cores")

    # Memory Analysis
    if stats.memory:
        _write_metric_analysis(f, "MEMORY", stats.memory, "GB")

    # Network Analysis
    if stats.network_rx or stats.network_tx:
        f.write("NETWORK ANALYSIS\n")
        f.write("-" * 30 + "\n")

        if stats.network_rx:
            f.write("Network RX:\n")
            _write_basic_metric_stats(f, stats.network_rx, "MB/s", indent="  ")
            _write_top_consumers(f, stats.network_rx, "network RX", "MB/s", indent="  ")

        if stats.network_tx:
            f.write("\nNetwork TX:\n")
            _write_basic_metric_stats(f, stats.network_tx, "MB/s", indent="  ")
            _write_top_consumers(f, stats.network_tx, "network TX", "MB/s", indent="  ")
        f.write("\n")

    # Filesystem Analysis
    if stats.filesystem:
        _write_metric_analysis(f, "FILESYSTEM", stats.filesystem, "GB")

    # GPU Analysis
    if stats.gpu_utilization or stats.gpu_memory:
        f.write("GPU ANALYSIS\n")
        f.write("-" * 30 + "\n")

        if stats.gpu_utilization:
            f.write("GPU Utilization:\n")
            _write_basic_metric_stats(f, stats.gpu_utilization, "%", indent="  ")

        if stats.gpu_memory:
            f.write("\nGPU Memory:\n")
            _write_basic_metric_stats(f, stats.gpu_memory, "%", indent="  ")
        f.write("\n")

    # Resource Efficiency Summary
    f.write("RESOURCE EFFICIENCY SUMMARY\n")
    f.write("-" * 30 + "\n")

    if stats.efficiency_metrics:
        for metric_name, value in stats.efficiency_metrics.items():
            f.write(f"  {metric_name.replace('_', ' ').title()}: {value}\n")
    else:
        f.write("  Insufficient data for efficiency analysis\n")

    f.write(f"\nReport generated successfully at {stats.generation_time}\n")


def _write_metric_analysis(f, metric_name: str, stats: MetricStatistics, unit: str):
    """Write analysis section for a specific metric."""
    f.write(f"{metric_name} ANALYSIS\n")
    f.write("-" * 30 + "\n")
    f.write(f"Total containers monitored: {stats.total_containers}\n")
    f.write(f"Total data points: {stats.total_data_points}\n")
    f.write(f"Average {metric_name.lower()} usage: {stats.average_value:.3f} {unit}\n")
    f.write(f"Peak {metric_name.lower()} usage: {stats.peak_value:.3f} {unit}\n")
    f.write(
        f"Total {metric_name.lower()} consumption: {stats.total_consumption_avg:.3f} {unit} (avg)\n"
    )
    f.write(
        f"Peak total {metric_name.lower()} consumption: {stats.peak_total_consumption:.3f} {unit}\n"
    )
    f.write(
        f"{metric_name} utilization variance: {stats.variance:.3f} (lower = more balanced)\n"
    )

    _write_top_consumers(f, stats, metric_name.lower(), unit)

    if stats.peak_hour:
        f.write(
            f"\nPeak {metric_name.lower()} usage period: {stats.peak_hour} ({stats.peak_hour_value:.3f} total {unit})\n"
        )

    f.write("\n")


def _write_basic_metric_stats(f, stats: MetricStatistics, unit: str, indent: str = ""):
    """Write basic statistics for a metric."""
    f.write(f"{indent}Total containers: {stats.total_containers}\n")
    f.write(f"{indent}Average rate: {stats.average_value:.2f} {unit}\n")
    f.write(f"{indent}Peak rate: {stats.peak_value:.2f} {unit}\n")
    f.write(
        f"{indent}Total throughput: {stats.total_consumption_avg:.2f} {unit} (avg)\n"
    )


def _write_top_consumers(
    f, stats: MetricStatistics, metric_name: str, unit: str, indent: str = ""
):
    """Write top consumers section for a metric."""
    if stats.top_consumers:
        f.write(f"\n{indent}Top {metric_name} consumers (average):\n")
        for container, values in stats.top_consumers.items():
            f.write(
                f"{indent}  {container}: {values['avg']:.3f} {unit} avg, {values['peak']:.3f} {unit} peak\n"
            )

    if stats.top_consumer_groups:
        f.write(f"\n{indent}Top {metric_name} consumer groups:\n")
        for group, values in stats.top_consumer_groups.items():
            f.write(
                f"{indent}  {group}: {values['avg']:.3f} {unit} avg, {values['peak']:.3f} {unit} peak\n"
            )


def main():
    parser = argparse.ArgumentParser(
        description="Analyze and plot resource usage from Prometheus data"
    )

    # Data source arguments
    parser.add_argument(
        "--data-dir",
        required=True,
        help="Directory containing Prometheus CSV files or output directory for collected data",
    )
    parser.add_argument("--tag", required=True, help="Tag for the experiment/agent")
    parser.add_argument(
        "--save-dir", default="./plots/resource", help="Directory to save plots"
    )
    parser.add_argument(
        "--generate-report", action="store_true", help="Generate text report"
    )

    # Data collection arguments
    parser.add_argument(
        "--collect-data",
        action="store_true",
        help="Collect data from Prometheus before plotting",
    )
    parser.add_argument(
        "--prometheus-url",
        default="http://localhost:9090",
        help="Prometheus server URL",
    )
    parser.add_argument(
        "--start-time",
        help="Start time for data collection (ISO format: 2024-01-01T00:00:00)",
    )
    parser.add_argument(
        "--end-time",
        help="End time for data collection (ISO format: 2024-01-01T01:00:00)",
    )
    parser.add_argument(
        "--step", default="15s", help="Query step interval for data collection"
    )

    # Container filtering arguments
    parser.add_argument(
        "--include-containers",
        nargs="+",
        help="Include only containers matching these names/patterns",
    )
    parser.add_argument(
        "--exclude-containers",
        nargs="+",
        help="Exclude containers matching these names/patterns",
    )
    parser.add_argument(
        "--max-containers",
        type=int,
        default=10,
        help="Maximum number of containers to show in plots",
    )
    parser.add_argument(
        "--aggregate-containers",
        nargs="+",
        help="Aggregate containers by these prefixes",
    )

    args = parser.parse_args()

    # Validate arguments for data collection mode
    if args.collect_data:
        if not args.start_time or not args.end_time:
            logger.error(
                "Error: --start-time and --end-time are required when --collect-data is specified"
            )
            sys.exit(1)

        try:
            start_time = datetime.fromisoformat(args.start_time)
            end_time = datetime.fromisoformat(args.end_time)
        except ValueError as e:
            logger.error(
                f"Error: Invalid time format. Use ISO format (YYYY-MM-DDTHH:MM:SS): {e}"
            )
            sys.exit(1)

        # Collect data from Prometheus
        print("=" * 60)
        print("COLLECTING PROMETHEUS DATA")
        print("=" * 60)

        success = collect_prometheus_data(
            args.prometheus_url,
            start_time,
            end_time,
            args.step,
            args.data_dir,
            args.tag,
        )

        if not success:
            logger.info("\nData collection failed. Checking for existing data files...")
            # Try to continue with existing files
            data = load_prometheus_data(args.data_dir, args.tag)
            if not data:
                logger.warning("No existing data found. Exiting.")
                sys.exit(1)
            logger.info("Found existing data files. Proceeding with analysis...")
        else:
            logger.info(f"\nData collection completed successfully!")
            # Load the newly collected data
            data = load_prometheus_data(args.data_dir, args.tag)
    else:
        # Load existing data
        print("=" * 60)
        print("LOADING EXISTING DATA")
        print("=" * 60)
        print(f"Loading Prometheus data from {args.data_dir}...")
        data = load_prometheus_data(args.data_dir, args.tag)

        if not data:
            logger.warning(f"No data found for tag '{args.tag}' in {args.data_dir}")
            logger.warning(
                "Consider using --collect-data to fetch data from Prometheus"
            )
            sys.exit(1)

    logger.info(f"Loaded {len(data)} metrics for analysis")

    # Generate plots
    print("\n" + "=" * 60)
    print("GENERATING PLOTS")
    print("=" * 60)

    os.makedirs(args.save_dir, exist_ok=True)

    logger.info("Generating GPU usage plots...")
    try:
        gpu_plot = plot_gpu_usage(data, args.tag, args.save_dir)
        logger.info(f"  ✓ Saved: {gpu_plot}")
    except Exception as e:
        logger.exception(f"  ✗ Error generating GPU plots: {e}")

    logger.info("Generating container resource plots...")
    try:
        container_plot = plot_container_resources(
            data,
            args.tag,
            args.save_dir,
            include_containers=args.include_containers,
            exclude_containers=args.exclude_containers,
            max_containers=args.max_containers,
            aggregate_prefixes=args.aggregate_containers,
        )
        logger.info(f"  ✓ Saved: {container_plot}")
    except Exception as e:
        logger.exception(f"  ✗ Error generating container plots: {e}")

    logger.info("Generating resource heatmaps...")
    try:
        heatmap_plot = plot_resource_heatmap(
            data,
            args.tag,
            args.save_dir,
            include_containers=args.include_containers,
            exclude_containers=args.exclude_containers,
            max_containers=args.max_containers,
            aggregate_prefixes=args.aggregate_containers,
        )
        logger.info(f"  ✓ Saved: {heatmap_plot}")
    except Exception as e:
        logger.exception(f"  ✗ Error generating heatmap plots: {e}")

    logger.info("Generating resource summary...")
    try:
        summary_plot = plot_resource_summary(
            data,
            args.tag,
            args.save_dir,
            include_containers=args.include_containers,
            exclude_containers=args.exclude_containers,
            aggregate_prefixes=args.aggregate_containers,
        )
        logger.info(f"  ✓ Saved: {summary_plot}")
    except Exception as e:
        logger.exception(f"  ✗ Error generating summary plots: {e}")

    # Generate report if requested
    if args.generate_report:
        logger.info("Generating resource report...")
        try:
            report_path = generate_resource_report(
                data,
                args.tag,
                args.save_dir,
                include_containers=args.include_containers,
                exclude_containers=args.exclude_containers,
                aggregate_prefixes=args.aggregate_containers,
            )
            logger.info(f"  ✓ Saved: {report_path}")
        except Exception as e:
            logger.exception(f"  ✗ Error generating report: {e}")

    print(f"\n" + "=" * 60)
    print("ANALYSIS COMPLETE")
    print("=" * 60)
    print(f"Results saved to: {args.save_dir}")


if __name__ == "__main__":
    main()
