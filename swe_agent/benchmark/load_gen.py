"""Script to run end-to-end evaluation on the benchmark"""

import argparse
import os
import sys
import dataclasses
from typing import Tuple
import json
import multiprocessing
import shutil
import yaml
import pandas as pd
from typing import Dict, List

from fast_service import (
    FastServiceConfig,
    FastServiceBenchmark,
    FastServiceBenchmarkConfig,
    FastServiceModuleBenchmark,
    FastServiceModuleBenchmarkConfig,
    StatefulModuleBenchmarkConfig,
    StatefulModuleBenchmark,
)

from fast_service.monitor.persistence.csv_impl.csv_persistence import auto_analysis
from fast_service import FastServiceConfig
from sweagent.request import (
    get_args,
    ScriptWarrper,
    swe_agent_request,
    ScriptArguments,
    VLLMRequest,
    VLLMResponse,
    vllm_request,
    VLLMGrouedpRequest,
    VLLMGroupedResponse,
    vllm_grouped_request,
)
from sweagent.environment.env_manager import SWE_fsm


def parse_args() -> Tuple[argparse.Namespace, ScriptArguments]:
    parser = argparse.ArgumentParser(
        description="Run end-to-end evaluation on the benchmark"
    )

    # benchmark loader
    parser.add_argument("--bm_target", default="sweagent", type=str)
    parser.add_argument("--bm_file_path", default="./.cache/sweagent_data.txt")
    parser.add_argument("--bm_config", default="config/swe_agent-client.yml")
    parser.add_argument("--bm_mode", default="one-by-one")
    parser.add_argument("--bm_request_num", default=100, type=int)
    parser.add_argument("--bm_nworkers", default=None, type=int)
    parser.add_argument("--bm_rank", default=None, type=int)
    parser.add_argument("--bm_module", type=str, default=None)
    parser.add_argument("--bm_rps", type=float, default=1.0)
    parser.add_argument("--bm_function", type=str, default=None)
    parser.add_argument("--bm_intermediate_storage", type=str, default=None)
    parser.add_argument("--bm_sequence_file_path", type=str, default=None)
    parser.add_argument("--bm_stateful_speedup", type=float, default=1.0)
    parser.add_argument("--bm_init_function", type=str, default=None)
    parser.add_argument("--bm_context_id_param", type=str, default=None)
    parser.add_argument("--bm_cleanup_function", type=str, default=None)
    parser.add_argument("--bm_stateful_match_policy", type=str, default="first")

    benchmark_args, remaining = parser.parse_known_args()

    return benchmark_args, get_args(remaining)


def line_to_req_sweagent(line: str):
    return ScriptWarrper(
        arguments=dataclasses.replace(
            sweagent_args,
            environment=dataclasses.replace(
                sweagent_args.environment,
                data_path=line.strip(),
            ),
        )
    )


def invoke_service_sweagent(request: ScriptWarrper):
    return swe_agent_request(request).success


def line_to_req_vllm(line: str):
    """Convert a line to a request for vLLM."""
    json_str = json.loads(line)
    return VLLMRequest(
        messages=json_str["messages"],
        model=json_str["model"],
        top_p=json_str.get("top_p", 0.9),
        request_id=json_str["request_id"],
    )


def invoke_service_vllm(request: VLLMRequest) -> bool:
    """Invoke the vLLM service with the given request."""
    return vllm_request(request).success


def group_vllm_request(source_path: str, target_path) -> None:
    """Group vLLM request into a groups by request_id."""
    with open(source_path, "r") as f:
        lines = f.readlines()

    requests = [line_to_req_vllm(line) for line in lines]
    grouped_request = VLLMGrouedpRequest(
        requests=requests,
        model=requests[0].model,
        top_p=requests[0].top_p,
    )

    response: VLLMGroupedResponse = vllm_grouped_request(grouped_request)

    with open(target_path, "w") as f:
        json.dump(response.to_dict(), f, indent=4)


def line_to_req_vllm_grouped(line: str):
    """Convert a line to a grouped request for vLLM."""
    json_str = json.loads(line)
    return VLLMGrouedpRequest(
        requests=json_str["requests"],
        model=json_str["model"],
        top_p=json_str.get("top_p", 0.9),
    )


def invoke_service_vllm_grouped(request: VLLMGrouedpRequest) -> bool:
    """Invoke the vLLM grouped service with the given request."""
    response: VLLMGroupedResponse = vllm_grouped_request(request)
    success = all(resp.success for resp in response.responses)
    return success


def run_benchmark(benchmark_args):
    if benchmark_args.bm_target == "sweagent":
        line_to_req = line_to_req_sweagent
        invoke_service = invoke_service_sweagent
    elif benchmark_args.bm_target == "vllm":
        line_to_req = line_to_req_vllm
        invoke_service = invoke_service_vllm
    elif benchmark_args.bm_target == "vllm_grouped":
        line_to_req = line_to_req_vllm_grouped
        invoke_service = invoke_service_vllm_grouped
        # Group vLLM requests if the source file exists
        if os.path.exists(benchmark_args.bm_file_path):
            grouped_file_path = benchmark_args.bm_file_path + ".grouped"
            group_vllm_request(benchmark_args.bm_file_path, grouped_file_path)
            benchmark_args.bm_file_path = grouped_file_path
    else:
        raise ValueError(
            f"Unsupported benchmark target: {benchmark_args.bm_target}. "
            "Supported targets are 'sweagent' and 'vllm'."
        )

    sampling_params = None
    bm_nworkers = benchmark_args.bm_nworkers
    bm_rank = benchmark_args.bm_rank
    if bm_nworkers is not None and bm_nworkers > 1:
        assert bm_rank is not None
        assert (
            bm_rank < bm_nworkers
        ), f"bm_rank {bm_rank} must be less than bm_nworkers {bm_nworkers}"
        sampling_params = {
            "method": "uniform",
            "step": bm_nworkers,
            "offset": bm_rank,
        }

    fast_service_config = FastServiceConfig.load_from_file(benchmark_args.bm_config)
    SWE_fsm.setup_client_mode(fast_service_config)

    bm_config = FastServiceBenchmarkConfig(
        file_path=benchmark_args.bm_file_path,
        mode=benchmark_args.bm_mode,
        lambda_rate=benchmark_args.bm_rps,
        request_num=benchmark_args.bm_request_num,
        sampling_params=sampling_params,
    )

    if benchmark_args.bm_module is not None:
        assert benchmark_args.bm_function is not None
        assert benchmark_args.bm_intermediate_storage is not None
        if benchmark_args.bm_function == "stateful":
            assert benchmark_args.bm_sequence_file_path is not None
            assert benchmark_args.bm_stateful_speedup > 0
            assert benchmark_args.bm_init_function is not None
            assert benchmark_args.bm_context_id_param is not None
            dir_path = os.path.join(
                benchmark_args.bm_intermediate_storage,
                "requests",
            )
            module_config = StatefulModuleBenchmarkConfig(
                module_name=benchmark_args.bm_module,
                sequence_file_path=benchmark_args.bm_sequence_file_path,
                dir_path=dir_path,
                base_config=bm_config,
                init_function=benchmark_args.bm_init_function,
                context_id_param=benchmark_args.bm_context_id_param,
                cleanup_function=benchmark_args.bm_cleanup_function,
                speedup_factor=benchmark_args.bm_stateful_speedup,
                match_policy=benchmark_args.bm_stateful_match_policy,
            )
            benchmark = StatefulModuleBenchmark(
                config=module_config, fast_service_manager=SWE_fsm
            )
        else:
            dir_path = os.path.join(
                benchmark_args.bm_intermediate_storage,
                "requests",
                f"{benchmark_args.bm_module}.{benchmark_args.bm_function}",
            )
            module_config = FastServiceModuleBenchmarkConfig(
                module_name=benchmark_args.bm_module,
                function_name=benchmark_args.bm_function,
                dir_path=dir_path,
                base_config=bm_config,
            )
            benchmark = FastServiceModuleBenchmark(
                config=module_config, fast_service_manager=SWE_fsm
            )
    else:
        benchmark = FastServiceBenchmark(
            config=bm_config, line_to_request=line_to_req, invoke_service=invoke_service
        )

    print("start benchmark", flush=True)
    benchmark.execute()
    print("end benchmark", flush=True)


if __name__ == "__main__":
    cmd_args = sys.argv[1:]
    print(f"Original command line arguments: {cmd_args}", flush=True)

    def worker_func(worker_config_file: str, rank: int, rps: float, nthreads: int):
        # run this file with worker_args in another process
        script_path = os.path.abspath(__file__)
        command = (
            f"FSBENCH_NUM_THREADS={nthreads} "
            + f"python {script_path} "
            + " ".join(cmd_args)
            + f" --bm_config {worker_config_file}"
            + f" --bm_rank {rank}"
            + f" --bm_rps {rps}"
        )
        print(f"Running command: {command}", flush=True)
        os.system(command)

    benchmark_args, sweagent_args = parse_args()
    original_cfg_file = benchmark_args.bm_config
    bm_nworkers = benchmark_args.bm_nworkers
    bm_rank = benchmark_args.bm_rank

    if bm_nworkers is not None and bm_nworkers > 1 and bm_rank is None:
        processes = []
        for i in range(bm_nworkers):
            config_file = original_cfg_file.replace(".yml", f"-{i}.yml")
            print(f"Using config file {config_file}", flush=True)

            # copy config file from original_cfg_file to config_file
            shutil.copy(original_cfg_file, config_file)
            # update monitor_storage in config_file
            with open(config_file, "r") as f:
                config = yaml.safe_load(f)
            config["monitor_storage"]["store_dir"] += f"-{i}"
            with open(config_file, "w") as f:
                yaml.dump(config, f)

            rps = benchmark_args.bm_rps / bm_nworkers
            max_nthreads = os.environ.get("FSBENCH_NUM_THREADS", 32)
            max_nthreads = int(max_nthreads)
            nthreads = int(max_nthreads / bm_nworkers)
            nthreads = max(1, nthreads)
            p = multiprocessing.Process(
                target=worker_func, args=(config_file, i, rps, nthreads)
            )
            processes.append(p)
            p.start()

        print("Waiting for all benchmark workers to finish...", flush=True)

        for p in processes:
            p.join()

        print(
            "All benchmark workers finished. Concatenating the results...", flush=True
        )

        # concate the monitor data from process to one file
        with open(original_cfg_file, "r") as f:
            config = yaml.safe_load(f)
            store_dir = config["monitor_storage"]["store_dir"]
            os.makedirs(store_dir)
        data: Dict[str, List[pd.DataFrame]] = {}
        COL_NAMES = [
            "end_ts",
            "request_id",
            "parent_service_name",
            "service_name",
            "delay",
            "call_id",
            "parent_call_id",
        ]
        for i in range(bm_nworkers):
            worker_store_dir = store_dir + f"-{i}"
            for filename in os.listdir(worker_store_dir):
                if not filename.endswith(".csv"):
                    continue
                if filename not in data:
                    data[filename] = []
                # read the csv file
                df = pd.read_csv(
                    os.path.join(worker_store_dir, filename), names=COL_NAMES
                )
                data[filename].append(df)
        # concat the data
        for filename, dfs in data.items():
            merged_df = pd.concat(dfs, ignore_index=True)
            # sort by end_ts
            merged_df = merged_df.sort_values(by="end_ts")
            # save merge_df, but ingoring the header
            merged_df.to_csv(
                os.path.join(store_dir, filename), header=False, index=False
            )
        auto_analysis(store_dir)
        print("Auto analysis done!")
    else:
        run_benchmark(benchmark_args)

    print("Benchmarking Done !", flush=True)
