FROM python:3.12
LABEL authors="fty1777"

# Install Docker CLI using the official Docker installation script
RUN curl -fsSL https://get.docker.com -o get-docker.sh && \
    sh get-docker.sh

# download and install dependencies for fast-service
WORKDIR /workspace/fast-service
COPY .cache/fast-service/requirements.txt requirements.txt
RUN pip install -r requirements.txt

# download and install dependencies for sweagent
WORKDIR /workspace/sweagent
COPY requirements.txt requirements.txt
RUN pip install -r requirements.txt

WORKDIR /workspace/fast-service
COPY .cache/fast-service/src src
COPY .cache/fast-service/pyproject.toml pyproject.toml
COPY .cache/fast-service/setup.py setup.py
RUN pip install -e .

# COPY .cache/agent-commons /workspace/agent-commons
# RUN cd /workspace/agent-commons && \
#     pip install -r requirements.txt && \
#     pip install -e .

# copy source code to docker image and install swe_agent
WORKDIR /workspace/sweagent
COPY src src
COPY pyproject.toml pyproject.toml
COPY config config
COPY trajectories/demonstrations trajectories/demonstrations

RUN pip install -e .

# set workdir and default command
WORKDIR /workspace/sweagent
