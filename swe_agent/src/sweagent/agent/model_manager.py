from pydantic import BaseModel
from sweagent.environment.env_manager import SWE_fsm
from fast_service import RequestContext
from sweagent.agent.models import get_model, ModelArguments, Command, APIStats

from concurrent.futures import ThreadPoolExecutor, Future
from queue import Queue
from threading import Lock, Thread
from typing import Callable, Dict, Optional


class ModelThreadManager:
    def __init__(self, max_threads: int | None = None):
        self.executor = ThreadPoolExecutor(max_threads)
        self.model_thread_map: Dict[str, Queue] = {}
        self.lock = Lock()

    def submit_task(
        self, model_id: str, func: Callable | str, *args, **kwargs
    ) -> Future:
        with self.lock:
            if model_id not in self.model_thread_map:
                raise ValueError(f"Environment with id {model_id} does not exist")

        # Task submission doesn't require holding the lock
        thread_queue = self.model_thread_map[model_id]
        future = Future()
        thread_queue.put((func, args, kwargs, future))
        return future

    def create_model(self, model_id: str, *args, **kwargs) -> Future:
        future = Future()
        with self.lock:
            if model_id in self.model_thread_map:
                future.set_exception(
                    ValueError(f"Environment with id {model_id} already exists")
                )
                return future

            # Create a thread-specific task queue
            thread_queue = Queue()
            self.model_thread_map[model_id] = thread_queue
            Thread(
                target=self._thread_worker,
                args=(thread_queue,),
                daemon=True,
            ).start()

        thread_queue.put(("create", args, kwargs, future))
        return future

    def destroy_model(self, model_id: str) -> Future:
        future = Future()
        with self.lock:
            if model_id not in self.model_thread_map:
                future.set_exception(
                    ValueError(f"Environment with id {model_id} does not exist")
                )
                return future

            thread_queue = self.model_thread_map.pop(model_id)

        thread_queue.put(("destroy", (), {}, future))
        return future

    def _thread_worker(self, task_queue: Queue):
        while True:
            func, args, kwargs, future = task_queue.get()
            try:
                if func == "create":
                    model = get_model(*args, **kwargs)
                    future.set_result(True)
                elif func == "destroy":
                    future.set_result(True)
                    break
                elif func == "query":
                    result = model.query(*args, **kwargs)
                    future.set_result(result)
                elif func == "reset_stats":
                    model.reset_stats(*args, **kwargs)
                    future.set_result(True)
                elif func == "history_to_messages":
                    result = model.history_to_messages(*args, **kwargs)
                    future.set_result(result)
                elif func == "get_stats":
                    result = model.stats
                    future.set_result(result)
                elif func == "replace_stats":
                    model.stats.replace(*args, **kwargs)
                    future.set_result(True)
                elif func == "add_stats":
                    model.stats += kwargs["other"]
                    future.set_result(True)
                else:
                    result = func(*args, **kwargs)
                    future.set_result(result)
            except Exception as e:
                future.set_exception(e)


_model_counter = 0
_model_counter_lock = Lock()
_model_thread_manager = ModelThreadManager()


class InitModelArgs(BaseModel):
    args: ModelArguments
    commands: list[Command] | None

    class Config:
        arbitrary_types_allowed = True


class InitModelReturn(BaseModel):
    model_id: str


@SWE_fsm.fast_service
def init_model(
    args: InitModelArgs, fsm_context: RequestContext = None
) -> InitModelReturn:
    global _model_counter

    with _model_counter_lock:
        model_id = _model_counter
        _model_counter += 1

    model_id = str(model_id)

    try:
        _model_thread_manager.create_model(model_id, args.args, args.commands).result()
    except Exception as e:
        raise Exception(f"Failed to create model: {e}")

    return InitModelReturn(model_id=model_id)


class QueryArgs(BaseModel):
    model_id: str
    history: list[dict[str, str | bool]]

    class Config:
        arbitrary_types_allowed = True


class QueryReturn(BaseModel):
    content: str


@SWE_fsm.fast_service
def query(args: QueryArgs, fsm_context: RequestContext = None) -> QueryReturn:
    try:
        ret = _model_thread_manager.submit_task(
            args.model_id, "query", args.history, fsm_context.call_id
        ).result()
        return QueryReturn(content=ret)
    except Exception as e:
        raise Exception(f"Failed to query model: {e}")


class ResetStatsArgs(BaseModel):
    model_id: str
    other: APIStats | None

    class Config:
        arbitrary_types_allowed = True


class ResetStatsReturn(BaseModel):
    success: bool


@SWE_fsm.fast_service
def reset_stats(
    args: ResetStatsArgs, fsm_context: RequestContext = None
) -> ResetStatsReturn:
    try:
        _model_thread_manager.submit_task(
            args.model_id, "reset_stats", args.other
        ).result()
        return ResetStatsReturn(success=True)
    except Exception as e:
        raise Exception(f"Failed to reset stats: {e}")


class HistoryToMessagesArgs(BaseModel):
    model_id: str
    history: list[dict[str, str]]
    is_demonstration: bool


class HistoryToMessagesReturn(BaseModel):
    messages: str | list[dict[str, str]]


@SWE_fsm.fast_service
def history_to_messages(
    args: HistoryToMessagesArgs, fsm_context: RequestContext = None
) -> HistoryToMessagesReturn:
    try:
        ret = _model_thread_manager.submit_task(
            args.model_id, "history_to_messages", args.history, args.is_demonstration
        ).result()
        return HistoryToMessagesReturn(messages=ret)
    except Exception as e:
        raise Exception(f"Failed to convert history to messages: {e}")


class GetStatsArgs(BaseModel):
    model_id: str


class GetStatsReturn(BaseModel):
    stats: APIStats


@SWE_fsm.fast_service
def get_stats(args: GetStatsArgs, fsm_context: RequestContext = None) -> GetStatsReturn:
    try:
        ret = _model_thread_manager.submit_task(args.model_id, "get_stats").result()
        return GetStatsReturn(stats=ret)
    except Exception as e:
        raise Exception(f"Failed to get stats: {e}")


class ReplaceStatsArgs(BaseModel):
    model_id: str
    other: APIStats

    class Config:
        arbitrary_types_allowed = True


class ReplaceStatsReturn(BaseModel):
    success: bool


@SWE_fsm.fast_service
def replace_stats(
    args: ReplaceStatsArgs, fsm_context: RequestContext = None
) -> ReplaceStatsReturn:
    try:
        _model_thread_manager.submit_task(
            args.model_id, "replace_stats", args.other
        ).result()
        return ReplaceStatsReturn(success=True)
    except Exception as e:
        raise Exception(f"Failed to replace stats: {e}")


class AddStatsArgs(BaseModel):
    model_id: str
    other: APIStats

    class Config:
        arbitrary_types_allowed = True


class AddStatsReturn(BaseModel):
    success: bool


@SWE_fsm.fast_service
def add_stats(args: AddStatsArgs, fsm_context: RequestContext = None) -> AddStatsReturn:
    try:
        _model_thread_manager.submit_task(
            args.model_id, "add_stats", other=args.other
        ).result()
        return AddStatsReturn(success=True)
    except Exception as e:
        raise Exception(f"Failed to add stats: {e}")
