# from __future__ import annotations
import logging
from git import Optional
from sweagent import CONFIG_DIR
from sweagent.utils.log import add_file_handler, get_logger

try:
    import rich
except ModuleNotFoundError as e:
    msg = (
        "You probably either forgot to install the dependencies "
        "or forgot to activate your conda or virtual environment."
    )
    raise RuntimeError(msg) from e
import json
import re
import traceback
import rich.console
import rich.markdown
import rich.panel

try:
    from rich_argparse import RichHelpFormatter
except ImportError:
    msg = "Please install the rich_argparse package with `pip install rich_argparse`."
    raise ImportError(msg)
import datetime
from dataclasses import dataclass
from getpass import getuser
from pathlib import Path
import yaml
from rich.markdown import Markdown
from simple_parsing import parse
from simple_parsing.helpers.flatten import FlattenedAccess
from simple_parsing.helpers.serialization.serializable import FrozenSerializable
from swebench.harness.constants import KEY_INSTANCE_ID, KEY_MODEL, KEY_PREDICTION
from unidiff import PatchSet
from sweagent.agent.agents import Agent, AgentArguments
from sweagent.agent.models import ModelArguments
from sweagent.environment.swe_env import EnvironmentArguments
from sweagent.environment.env_manager import (
    env_init,
    EnvironmentInitArguments,
    env_reset_container,
    EnvironmentContainerResetArguments,
    env_reset,
    EnvironmentResetArguments,
    env_close,
    EnvironmentCloseArguments,
    env_get_attr,
    EnvironmentGetAttrArguments,
)
from sweagent.environment.utils import (
    extract_flag_format,
    get_data_path_name,
)
from pydantic import BaseModel
from fast_service import RequestContext
from sweagent.environment.env_manager import SWE_fsm

__doc__: str = """ Run inference. Usage examples:

```bash
# Run over a github issue:
python run.py --model_name "gpt4" --data_path "https://github.com/pvlib/pvlib-python/issues/1603" --config_file "config/default_from_url.yaml"
# Apply a patch in a local repository to an issue specified as Markdown file and run a custom installer script in the container
python run.py --model_name "gpt4" --data_path "/path/to/my_issue.md" --repo_path "/path/to/my/local/repo" --environment_setup "/path/to/setup.sh" --config_file "config/default_from_url.yaml" --apply_patch_locally
```

**For more information**: https://princeton-nlp.github.io/SWE-agent/usage/cl_tutorial/
"""


logger = get_logger("swe-agent-run")
logging.getLogger("simple_parsing").setLevel(logging.WARNING)


@dataclass(frozen=True)
class ActionsArguments(FlattenedAccess, FrozenSerializable):
    """Run real-life actions (opening PRs, etc.) if we can solve the issue."""

    # Open a PR with the patch if we can solve the issue
    open_pr: bool = False
    # When working with local repository: Apply patch
    apply_patch_locally: bool = False
    # Option to be used with open_pr: Skip action if there are already commits claiming
    # to fix the issue. Please only set this to False if you are sure the commits are
    # not fixes or if this is your own repository!
    skip_if_commits_reference_issue: bool = True
    # OBSOLETE. Do not use, will raise error. Please specify --repo_path instead.
    push_gh_repo_url: str = ""

    def __post_init__(self):
        if self.push_gh_repo_url:
            msg = "push_gh_repo_url is obsolete. Use repo_path instead"
            raise ValueError(msg)


def str_to_path(value: Optional[str]) -> Optional[Path]:
    """Convert a single string or None to a Path."""
    return Path(value) if value else None


@dataclass(frozen=True)
class ScriptArguments(FlattenedAccess, FrozenSerializable):
    """Configure the control flow of the run.py script"""

    environment: EnvironmentArguments
    agent: AgentArguments
    actions: ActionsArguments
    # Only run instances that completely match this regex
    instance_filter: str = ".*"
    # Skip instances with existing trajectories
    skip_existing: bool = True
    # Suffix for the run name (used for example in trajectory directory naming)
    suffix: str = ""
    # Raise unhandled exceptions during the run (useful for debugging)
    raise_exceptions: bool = False
    # Dump the entire config to the log
    print_config: bool = True
    # Run the agent in CTF mode (SWE-agent: EnIGMA)
    ctf: bool = False

    @property
    def run_name(self) -> str:
        """Generate a unique name for this run based on the arguments."""
        model_name = self.agent.model.model_name.replace(":", "-")
        data_stem = get_data_path_name(self.environment.data_path)
        assert str_to_path(self.agent.config_file) is not None  # mypy
        config_stem = Path(self.agent.config_file).stem

        temp = self.agent.model.temperature
        top_p = self.agent.model.top_p

        per_instance_cost_limit = self.agent.model.per_instance_cost_limit
        install_env = self.environment.install_environment

        return (
            f"{model_name}__{data_stem}__{config_stem}__t-{temp:.2f}__p-{top_p:.2f}"
            + f"__c-{per_instance_cost_limit:.2f}__install-{int(install_env)}"
            + (f"__{self.suffix}" if self.suffix else "")
        )


class _ContinueLoop(Exception):
    """Used for internal control flow"""


class Main:
    def __init__(self, args: ScriptArguments, fsm_context: RequestContext = None):
        self.fsm_context = fsm_context
        self.traj_dir = Path("trajectories") / Path(getuser()) / args.run_name
        self.traj_dir.mkdir(parents=True, exist_ok=True)
        timestamp = datetime.datetime.now().strftime("%y%m%d%H%M%S")
        log_path = self.traj_dir / f"run-{timestamp}.log"
        logger.info("Logging to %s", log_path)
        add_file_handler(log_path)
        if args.print_config:
            logger.debug(f"📙 Arguments: {args.dumps_yaml()}")
        self.args = args
        self.agent = Agent("primary", args.agent, fsm_context=self.fsm_context)
        self._save_arguments()

    def run(self, index: int) -> None:
        env_id = self.env_id
        # Reset environment
        data = env_get_attr(
            args=EnvironmentGetAttrArguments(env_id=env_id, name="data"),
            fsm_context=self.fsm_context,
        ).value
        instance_id = data[index]["instance_id"]
        assert isinstance(instance_id, str)  # mypy
        if self.should_skip(instance_id):
            raise _ContinueLoop
        logger.info("▶️  Beginning task " + str(self.fsm_context.request_id))

        ret = env_reset(
            args=EnvironmentResetArguments(env_id=env_id, index=index),
            fsm_context=self.fsm_context,
        )
        if ret.success is False:
            logger.error(
                "Error resetting environment. The code repo might be deleted or moved "
                "to somewhere else. Skipping this task."
            )
            raise _ContinueLoop
        assert ret.output is not None
        observation, info = ret.output
        if info is None:
            raise _ContinueLoop

        # Get info, patch information
        # issue = getattr(self.env, "query", None)
        issue = env_get_attr(
            args=EnvironmentGetAttrArguments(env_id=env_id, name="query"),
            fsm_context=self.fsm_context,
        )
        files = []

        record = env_get_attr(
            args=EnvironmentGetAttrArguments(env_id=env_id, name="record"),
            fsm_context=self.fsm_context,
        ).value
        assert record is not None  # mypy
        if "patch" in record:
            files = "\n".join(
                [f"- {x.path}" for x in PatchSet(record["patch"]).modified_files]
            )
        # Get test files, F2P tests information
        test_files = []
        if "test_patch" in record:
            test_patch_obj = PatchSet(record["test_patch"])
            test_files = "\n".join(
                [
                    f"- {x.path}"
                    for x in test_patch_obj.modified_files + test_patch_obj.added_files
                ]
            )
        tests = ""
        if "FAIL_endTO_PASS" in record:
            tests = "\n".join([f"- {x}" for x in record["FAIL_TO_PASS"]])

        setup_args = {
            "issue": issue,
            "files": files,
            "test_files": test_files,
            "tests": tests,
        }
        challenge = env_get_attr(
            args=EnvironmentGetAttrArguments(env_id=env_id, name="challenge"),
            fsm_context=self.fsm_context,
        ).value
        if challenge is not None:
            setup_args["flag_format"] = extract_flag_format(challenge["flag"])
            setup_args["name"] = challenge["name"]
            setup_args["description"] = challenge["description"]
            setup_args["category_friendly"] = challenge["category_friendly"]
            setup_args["points"] = challenge["points"]
            setup_args["files"] = (
                challenge["files"] or "No files included in this challenge."
            )
            setup_args["box"] = challenge.get("server_name")
            setup_args["port"] = challenge.get("port")
            setup_args["server_description"] = challenge.get("server_description")
        info, trajectory = self.agent.run(
            env_id=env_id,
            setup_args=setup_args,
            observation=observation,
            traj_dir=self.traj_dir,
            return_type="info_trajectory",
        )
        # This saves results
        # self._save_predictions(instance_id, info, challenge)

    def main(self):
        env_id = env_init(
            args=EnvironmentInitArguments(args=self.args.environment),
            fsm_context=self.fsm_context,
        ).env_id
        self.env_id = env_id
        data = env_get_attr(
            args=EnvironmentGetAttrArguments(env_id=env_id, name="data"),
            fsm_context=self.fsm_context,
        ).value

        for index in range(len(data)):
            try:
                self.run(index)
            except _ContinueLoop:
                continue
            except KeyboardInterrupt:
                logger.info("Exiting InterCode environment...")
                env_close(
                    args=EnvironmentCloseArguments(env_id=env_id),
                    fsm_context=self.fsm_context,
                )
                break
            except SystemExit:
                logger.critical("❌ Exiting because SystemExit was called")
                env_close(
                    args=EnvironmentCloseArguments(env_id=env_id),
                    fsm_context=self.fsm_context,
                )
                logger.info("Container closed")
                raise
            except Exception as e:
                logger.warning(traceback.format_exc())
                if self.args.raise_exceptions:
                    env_close(
                        args=EnvironmentCloseArguments(env_id=env_id),
                        fsm_context=self.fsm_context,
                    )
                    raise e
                record = env_get_attr(
                    args=EnvironmentGetAttrArguments(env_id=env_id, name="record"),
                    fsm_context=self.fsm_context,
                ).value
                if record:
                    logger.warning(f"❌ Failed on {record['instance_id']}: {e}")
                else:
                    logger.warning("❌ Failed on unknown instance")
                env_reset_container(
                    args=EnvironmentContainerResetArguments(env_id=env_id),
                    fsm_context=self.fsm_context,
                )
                continue
        env_close(
            args=EnvironmentCloseArguments(env_id=env_id),
            fsm_context=self.fsm_context,
        )

    def _save_arguments(self) -> None:
        """Save the arguments to a yaml file to the run's trajectory directory."""
        log_path = self.traj_dir / "args.yaml"

        if log_path.exists():
            try:
                other_args = self.args.load_yaml(log_path)
                if (
                    self.args.dumps_yaml() != other_args.dumps_yaml()
                ):  # check yaml equality instead of object equality
                    logger.warning("**************************************************")
                    logger.warning("Found existing args.yaml with different arguments!")
                    logger.warning("**************************************************")
            except Exception:
                logger.warning(
                    f"Failed to load existing args.yaml: {traceback.format_exc()}"
                )

        with log_path.open("w") as f:
            self.args.dump_yaml(f)

    def should_skip(self, instance_id: str) -> bool:
        """Check if we should skip this instance based on the instance filter and skip_existing flag."""
        # Skip instances that don't match the instance filter
        if re.match(self.args.instance_filter, instance_id) is None:
            logger.info(
                f"⏭️ Instance filter not matched. Skipping instance {instance_id}"
            )
            return True

        # If flag is set to False, don't skip
        if not self.args.skip_existing:
            return False

        # Check if there's an existing trajectory for this instance
        log_path = self.traj_dir / (instance_id + ".traj")
        if not log_path.exists():
            return False

        content = log_path.read_text()
        if not content.strip():
            logger.warning("Found empty trajectory: %s. Removing.", log_path)
            log_path.unlink()
            return False

        data = json.loads(content)
        # If the trajectory has no exit status, it's incomplete and we will redo it
        exit_status = data["info"].get("exit_status", None)
        if exit_status == "early_exit" or exit_status is None:
            logger.warning(
                f"Found existing trajectory with no exit status: {log_path}. Removing."
            )
            log_path.unlink()
            return False

        logger.info(f"⏭️ Skipping existing trajectory: {log_path}")
        return True

    def _save_predictions(
        self, instance_id: str, info, challenge: dict[str, str] | None
    ):
        output_file = self.traj_dir / "all_preds.jsonl"
        model_patch = info["submission"] if "submission" in info else None
        datum = {
            KEY_MODEL: Path(self.traj_dir).name,
            KEY_INSTANCE_ID: instance_id,
            KEY_PREDICTION: model_patch,
        }
        if challenge is not None:
            challenge_datum = {
                "challenge_name": challenge["name"],
                "challenge_category": challenge["category"],
                "challenge_path": challenge["file_path"],
            }
            datum.update(challenge_datum)
        with open(output_file, "a+") as fp:
            print(json.dumps(datum), file=fp, flush=True)
        logger.info(f"Saved predictions to {output_file}")


def get_args(args=None) -> ScriptArguments:
    """Parse command line arguments and return a ScriptArguments object.

    Args:
        args: Optional list of arguments to parse. If not provided, uses sys.argv.
    """
    defaults = ScriptArguments(
        suffix="",
        environment=EnvironmentArguments(
            image_name="sweagent/swe-agent:latest",
            data_path="princeton-nlp/SWE-bench_Lite",
            split="dev",
            verbose=True,
            install_environment=True,
            cache_task_images=False,
        ),
        skip_existing=False,
        raise_exceptions=False,
        print_config=True,
        instance_filter=".*",
        agent=AgentArguments(
            model=ModelArguments(
                model_name="gpt4",
                total_cost_limit=0.0,
                per_instance_cost_limit=1_000_000.0,
                temperature=0.0,
                top_p=0.95,
            ),
            config_file=CONFIG_DIR / "default.yaml",
        ),
        actions=ActionsArguments(
            open_pr=False,
            apply_patch_locally=False,
            skip_if_commits_reference_issue=True,
            push_gh_repo_url="",
        ),
        ctf=False,
    )

    # Nicer yaml dumping of multiline strings
    def multiline_representer(dumper, data):
        """configures yaml for dumping multiline strings
        Ref: https://stackoverflow.com/questions/8640959/how-can-i-control-what-scalar-form-pyyaml-uses-for-my-data
        """
        if data.count("\n") > 0:  # check for multiline string
            return dumper.represent_scalar("tag:yaml.org,2002:str", data, style="|")
        return dumper.represent_scalar("tag:yaml.org,2002:str", data)

    yaml.add_representer(str, multiline_representer)

    return parse(
        ScriptArguments,
        default=defaults,
        add_config_path_arg=False,
        args=args,
        formatter_class=RichHelpFormatter,
        description=Markdown(__doc__),
    )


class ScriptWarrper(BaseModel):
    arguments: ScriptArguments

    class Config:
        arbitrary_types_allowed = True
        json_encoders = {Path: lambda v: str(v)}


class SweAgentRequestResponse(BaseModel):
    success: bool


@SWE_fsm.fast_service
def swe_agent_request(
    args: ScriptWarrper, fsm_context: RequestContext = None
) -> SweAgentRequestResponse:
    Main(args.arguments, fsm_context).main()
    return SweAgentRequestResponse(success=True)


from openai import OpenAI
from openai.types.chat import ChatCompletion
from .utils.config import keys_config

local_api_base_url: str | None = keys_config.get("OPENAI_API_BASE_URL", None)
local_llm_client = OpenAI(
    api_key=keys_config["OPENAI_API_KEY"], base_url=local_api_base_url
)


class VLLMRequest(BaseModel):
    """Request to run a VLLM model."""

    messages: list[dict[str, str]] = []
    model: str = "deepseek-ai/DeepSeek-R1-Distill-Qwen-32B"
    top_p: float = 0.95
    request_id: str | None = None


class VLLMResponse(BaseModel):
    """Response from a VLLM model request."""

    success: bool
    output: ChatCompletion | None = None
    error: str | None = None


@SWE_fsm.fast_service
def vllm_request(item: VLLMRequest, fsm_context: RequestContext = None) -> VLLMResponse:
    """Run a VLLM model with the given request parameters."""
    try:
        response = local_llm_client.chat.completions.create(
            model=item.model,
            messages=item.messages,
            top_p=item.top_p,
        )
        return VLLMResponse(success=True, output=response)
    except Exception as e:
        logger.error(f"Error in VLLM request: {e}")
        return VLLMResponse(success=False, error=str(e))


class VLLMGrouedpRequest(BaseModel):
    """Request to run a VLLM model."""

    requests: list[VLLMRequest] = []


class VLLMGroupedResponse(BaseModel):
    """Response from a VLLM model request."""

    responses: list[VLLMResponse] = []


@SWE_fsm.fast_service
def vllm_grouped_request(
    item: VLLMGrouedpRequest, fsm_context: RequestContext = None
) -> VLLMGroupedResponse:
    """Run a VLLM model with the given request parameters."""
    responses = []
    for req in item.requests:
        response = vllm_request(req, fsm_context=fsm_context)
        responses.append(response)
    return VLLMGroupedResponse(responses=responses)
