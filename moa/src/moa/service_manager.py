from typing import Optional
from pydantic import BaseModel
from fast_service import FastServiceManager

from .moa_config import MoAConfig

MoA_SERVICE_MANAGER = FastServiceManager()

_MoA_CONFIG: Optional[MoAConfig] = None


def init(cfg_file):
    global _MoA_CONFIG

    _MoA_CONFIG = MoAConfig.load_from_file(cfg_file)


def get_moa_config() -> MoAConfig:
    return _MoA_CONFIG


def _get_final_system_prompt(references: list[str]) -> str:
    return (
        _MoA_CONFIG.prompt_template
        + "\n"
        + "\n".join([f"{i + 1}. {element}" for i, element in enumerate(references)])
    )


class MoAInput(BaseModel):
    raw_question: str
    references: list[str] | None

    def to_messages(self):
        messages = (
            [
                {
                    "role": "system",
                    "content": _get_final_system_prompt(self.references),
                },
                {"role": "user", "content": self.raw_question},
            ]
            if self.references
            else [{"role": "user", "content": self.raw_question}]
        )
        return messages


class MoAOutput(BaseModel):
    answer: str


class LayerOutput(BaseModel):
    resp_list: list[str]


class AgentOutput(BaseModel):
    response: str
