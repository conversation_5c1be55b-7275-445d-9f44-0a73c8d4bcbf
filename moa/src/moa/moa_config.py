import yaml

from typing import Optional
from pydantic import BaseModel


def _read_yaml(path: str) -> dict:
    result = None
    with open(path, "r", encoding="utf-8") as f:
        result = yaml.load(f.read(), Loader=yaml.FullLoader)
    if result is None:
        raise IOError(f"Fail to read yaml file {path}")
    if not isinstance(result, dict):
        raise SyntaxError(f"Only support dict style yaml file.")
    return result


class ModelConfig(BaseModel):
    name: str
    ip: str
    port: int
    model_name: str
    api_key: Optional[str] = "EMPTY"

    @classmethod
    def from_cfg(
        cls, name: str, ip: str, port: int, model_name: str, api_key: str = "EMPTY"
    ):
        return ModelConfig(
            name=name, ip=ip, port=port, model_name=model_name, api_key=api_key
        )


class LayerConfig(BaseModel):
    index: int
    proposer_list: list[ModelConfig]

    @classmethod
    def from_cfg(cls, index: int, proposer_list: list[dict]):
        return LayerConfig(
            index=index,
            proposer_list=[ModelConfig.from_cfg(**_) for _ in proposer_list],
        )


class MoAConfig:
    layer_list: list[LayerConfig]
    aggregator: ModelConfig
    prompt_template: str
    max_completion_tokens: int

    def __init__(
        self,
        layer_list: list[dict],
        aggregator: dict,
        prompt_template: str,
        max_completion_tokens: int,
    ):
        self.layer_list = [LayerConfig.from_cfg(**_) for _ in layer_list]
        self.aggregator = ModelConfig.from_cfg(**aggregator)
        self.prompt_template = prompt_template
        self.max_completion_tokens = max_completion_tokens

    @classmethod
    def load_from_file(cls, path: str):
        config_dict = _read_yaml(path)

        return MoAConfig(**config_dict)
