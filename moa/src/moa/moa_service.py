import asyncio

from functools import lru_cache
from typing import Callable

from fast_service import RequestContext
from agent_commons.llm import Async<PERSON><PERSON><PERSON>, LLMClientFactory, LLMResponse

from .service_manager import (
    MoA_SERVICE_MANAGER,
    MoAInput,
    MoAOutput,
    get_moa_config,
    LayerOutput,
    AgentOutput,
)
from .moa_config import MoAConfig, LayerConfig, ModelConfig


_MoA_CONFIG: MoAConfig
_AGENT_TABLE: list[list[Callable]]


@lru_cache
def get_llm_client(api_key, ip, port) -> AsyncLLMClient:
    return LLMClientFactory.create_async_client(
        api_key=api_key, base_url=f"http://{ip}:{port}/v1"
    )


def llm_invocation_function_factory(name) -> Callable:

    async def call_llm(
        layer_input: MoAInput, proposer: ModelConfig, context: RequestContext
    ) -> AgentOutput:
        client: AsyncLLMClient = get_llm_client(
            proposer.api_key, proposer.ip, proposer.port
        )
        resp: LLMResponse = await client.call_llm(
            model=proposer.model_name,
            messages=layer_input.to_messages(),
            call_id=context.call_id,
            max_completion_tokens=_MoA_CONFIG.max_completion_tokens,
        )
        return AgentOutput(response=resp.content)

    call_llm.__name__ = name

    return MoA_SERVICE_MANAGER.async_fast_service(call_llm)


def init():
    global _MoA_CONFIG, _AGENT_TABLE
    _MoA_CONFIG = get_moa_config()

    _AGENT_TABLE = []
    layer_index = 0
    for layer in _MoA_CONFIG.layer_list:
        layer_function_list: list[Callable] = []
        proposer_index = 0
        for proposer in layer.proposer_list:
            layer_function_list.append(
                llm_invocation_function_factory(
                    f"proposer_{layer_index}_{proposer_index}"
                )
            )
            proposer_index += 1
        _AGENT_TABLE.append(layer_function_list)
        layer_index += 1
    _AGENT_TABLE.append([llm_invocation_function_factory(f"aggregator")])


async def async_llm_chat(
    layer_input: MoAInput,
    proposer: ModelConfig,
    proposer_index: tuple[int, int],
    context: RequestContext,
) -> str:
    global _AGENT_TABLE
    agent_output: AgentOutput = await _AGENT_TABLE[proposer_index[0]][
        proposer_index[1]
    ](layer_input, proposer, context=context)
    return agent_output.response


async def gather_layer_resp(
    layer_input: MoAInput, layer: LayerConfig, context: RequestContext
) -> list[str]:
    parallel_context = RequestContext(context.request_id, call_id=context.call_id)
    parallel_context.set_parent_context(context)
    return await asyncio.gather(
        *[
            async_llm_chat(
                layer_input,
                proposer,
                (layer.index, idx),
                context=parallel_context,
            )
            for idx, proposer in enumerate(layer.proposer_list)
        ]
    )


@MoA_SERVICE_MANAGER.async_fast_service
async def layer_inference(
    layer_input: MoAInput, layer: LayerConfig, context: RequestContext
) -> LayerOutput:
    resp_list = await gather_layer_resp(layer_input, layer, context=context)
    return LayerOutput(resp_list=resp_list)


@MoA_SERVICE_MANAGER.async_fast_service
async def moa_chat(req: MoAInput, context: RequestContext = None) -> MoAOutput:
    layer_input = req

    for layer in _MoA_CONFIG.layer_list:
        layer_resp = await layer_inference(layer_input, layer, context=context)
        layer_input = MoAInput(
            raw_question=layer_input.raw_question,
            references=[str(proposer_resp) for proposer_resp in layer_resp],
        )

    resp: AgentOutput = await _AGENT_TABLE[-1][0](
        layer_input, _MoA_CONFIG.aggregator, context=context
    )

    return MoAOutput(answer=resp.response)
