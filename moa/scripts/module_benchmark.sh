SCRIPT_DIR=$(dirname "$(realpath "$0")")
MOA_DIR=$(dirname "$SCRIPT_DIR")

export MOA_HOME=${MOA_DIR}
export MOA_MODULE_PORT=$3

MODULE=$2

# aggregator, layer_inference, proposer_{i}_{j}
if [[ -z ${MODULE} ]]; then
  export MOA_MODULE=aggregator
else
  export MOA_MODULE=${MODULE}
fi

# prepare config for module benchmark
MODULE_BM_DIR=${MOA_DIR}/.cache/module_bm/${MOA_MODULE}
MODULE_CONFIG_DIR=${MODULE_BM_DIR}/config

mkdir -p ${MODULE_CONFIG_DIR}

cp ${MOA_DIR}/config/moa_config.yml ${MODULE_CONFIG_DIR}

CONFIG_TEMPLATE_ARRAY=("moa_module_service.yml" "moa_module_client.yml")


for CONFIG_FILE in "${CONFIG_TEMPLATE_ARRAY[@]}"; do
    sed -e  "s|\${MOA_MODULE}|${MOA_MODULE}|g" \
        -e  "s|\${MOA_MODULE_PORT}|${MOA_MODULE_PORT}|g" \
        ${MOA_DIR}/config/${CONFIG_FILE} > ${MODULE_CONFIG_DIR}/${CONFIG_FILE/_module_/_}
done

# prepare docker compose for module benchmark
sed -e  "s|\${MOA_MODULE}|${MOA_MODULE}|g" \
    -e  "s|\${MOA_MODULE_PORT}|${MOA_MODULE_PORT}|g" \
    ${MOA_DIR}/../benchmarking/moa/moa-module.yaml > ${MODULE_BM_DIR}/moa-module.yaml

COMMAND=$1

if [[ ${COMMAND} = "up" ]]; then
  docker compose -f ${MODULE_BM_DIR}/moa-module.yaml up -d
else
  docker compose -f ${MODULE_BM_DIR}/moa-module.yaml down
fi


