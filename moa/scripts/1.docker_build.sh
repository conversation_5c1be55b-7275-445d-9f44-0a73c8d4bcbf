# AGENT_COMMONS_HOME=""
# FAST_SERVICE_HOME=""

SCRIPT_DIR=$(dirname "$(realpath "$0")")
MOA_DIR=$(dirname "$SCRIPT_DIR")


AGENT_COMMONS_COPY_DIR=${MOA_DIR}/.cache/agent-commons

if [[ ! -d "${AGENT_COMMONS_COPY_DIR}" ]]; then
  if [[ -z "$AGENT_COMMONS_HOME" ]]; then
    echo "AGENT_COMMONS_HOME should be set for lib copy."
  else
    mkdir -p ${AGENT_COMMONS_COPY_DIR}
    cp -r ${AGENT_COMMONS_HOME}/* ${AGENT_COMMONS_COPY_DIR}
  fi
fi

FAST_SERVICE_COPY_DIR=${MOA_DIR}/.cache/fast-service
if [[ ! -d "${FAST_SERVICE_COPY_DIR}" ]]; then
  if [[ -z "$FAST_SERVICE_HOME" ]]; then
    echo "FAST_SERVICE_HOME should be set for lib copy."
  else
    mkdir -p ${FAST_SERVICE_COPY_DIR}
    cp -r ${FAST_SERVICE_HOME}/* ${FAST_SERVICE_COPY_DIR}
  fi
fi


cd ${MOA_DIR}
docker build -t moa:v1 .