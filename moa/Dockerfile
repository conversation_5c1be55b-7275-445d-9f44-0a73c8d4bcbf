FROM python:3.12
LABEL authors="<PERSON>"

# download and install fast-service
COPY .cache/fast-service /workspace/fast-service
RUN cd /workspace/fast-service && \
    pip install -r requirements.txt && \
    pip install -e .

COPY .cache/agent-commons /workspace/agent-commons
RUN cd /workspace/agent-commons && \
    pip install -r requirements.txt && \
    pip install -e .

# install dependencies for moa
WORKDIR /workspace/moa
COPY requirements.txt requirements.txt
RUN pip install -r requirements.txt

# copy necessary code to docker image
COPY src src
COPY scripts scripts

ENV MOA_HOME=/workspace/moa
WORKDIR /workspace/moa
