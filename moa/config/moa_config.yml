aggregator:
  name: aggregator
  ip: 127.0.0.1
  port: 7000
  model_name: Qwen/Qwen2.5-1.5B-Instruct

layer_list:
  - index: 0
    proposer_list:
      - name: proposer-0-0
        ip: 127.0.0.1
        port: 7000
        model_name: Qwen/Qwen2.5-1.5B-Instruct
      - name: proposer-0-1
        ip: 127.0.0.1
        port: 7000
        model_name: Qwen/Qwen2.5-1.5B-Instruct
  - index: 1
    proposer_list:
      - name: proposer-1-0
        ip: 127.0.0.1
        port: 7000
        model_name: Qwen/Qwen2.5-1.5B-Instruct
      - name: proposer-1-1
        ip: 127.0.0.1
        port: 7000
        model_name: Qwen/Qwen2.5-1.5B-Instruct
  - index: 2
    proposer_list:
      - name: proposer-2-0
        ip: 127.0.0.1
        port: 7000
        model_name: Qwen/Qwen2.5-1.5B-Instruct
      - name: proposer-2-1
        ip: 127.0.0.1
        port: 7000
        model_name: <PERSON>wen/Qwen2.5-1.5B-Instruct

prompt_template: > 
  You have been provided with a set of responses from various open-source models to the latest user query.
  Your task is to synthesize these responses into a single, high-quality response.
  It is crucial to critically evaluate the information provided in these responses,
  recognizing that some of it may be biased or incorrect.
  Your response should not simply replicate the given answers
  but should offer a refined, accurate, and comprehensive reply to the instruction.
  Ensure your response is well-structured, coherent, and adheres to the highest standards of accuracy and reliability.
  Responses from models:

max_completion_tokens: 10000